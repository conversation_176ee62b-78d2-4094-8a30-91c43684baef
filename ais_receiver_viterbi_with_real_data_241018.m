clear all;

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
BIT_RATE = 9600;                    % Bit rate
OSR = 5;                            % Over sampling rate
BT = 0.4;                           % Transmit BT product
RX_BT = 0.5;                        % Receive BT product

PKT_PERIOD = 256;                   % Packet period
PKT_PERIOD_OS = PKT_PERIOD * OSR;
LEN_PSF = 8 * OSR;                  % Pulse shaping filter length

H_NORM = 2;                         % 1: normalized by h_max, 2: normalized by norm(h)
AMP = 200;                          % Amplitude
DC_LEVEL = 0;                       % DC level
NUM_OF_PACKETS = 1;                 % Number of packets

SNR_DB_100 = 25;                    % SNR in dB 

SNR_DB_110 = SNR_DB_100 - 10;
SNR_DB_115 = SNR_DB_100 - 15;
STD_100 = sqrt(10^(-SNR_DB_100 / 10));
STD_110 = sqrt(10^(-SNR_DB_110 / 10));
STD_115 = sqrt(10^(-SNR_DB_115 / 10));

USE_RX_FILTER = 1;

USE_LEGACY_MODEM = 1;
if (USE_LEGACY_MODEM == 1)
    % Legacy modem defines
    NOTCH_FLT_A  = [+1.999986841577810, -0.999986910116283];
    NOTCH_FLT_B  = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
    % BT=0.5
    G_vRxGmskCoefficient = [8, 187, 2427, 17821, 74016, 173924, 231226, 173924, 74016, 17821, 2427, 187, 8];

    DC_MIN_LEVEL = floor(  50 * 4095 / 3300);    % 0.05V
    DC_MID_LEVEL = floor(1000 * 4095 / 3300);    % 1.00V
    DC_MAX_LEVEL = floor(2000 * 4095 / 3300);    % 2.00V

    RX_PLL_FULL  = 2400;
    RX_PLL_HALF  = (RX_PLL_FULL / 2);
    RX_PLL_INCR  = (RX_PLL_FULL / OSR);
    RX_PLL_STEP  = (RX_PLL_INCR / 3);

    RX_GMSK_BT_0_4_FIR_N   = 17;
    RX_GMSK_BT_0_5_FIR_N   = 13;
    RX_GMSK_TO_INT_FACTOR  = 16;

    RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

    RX_MDM_STATUS_PREAMBLE = 0;
    RX_MDM_STATUS_START    = 1;
    RX_MDM_STATUS_PRELOAD  = 2;
    RX_MDM_STATUS_DATA     = 3;

    RX_DOT_MAX_CNT_SIZE    = 7;
    RX_DOT_MAX_CNT_MASK    = 0x7f;
    RX_DOT_START_P_MASK    = 0x05;
    RX_DOT_DETCT_P_MASK    = 0x55;
    RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

    RX_PRE_MAX_CNT_SIZE    = 12;
    RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

    G_vNotchDataX = zeros(1, 3);

    G_vReverDataTableX     = [ ...
     %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
         0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
         8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
         4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
        12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
         2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
        10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
         6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
        14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
         1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
         9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
         5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
        13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
         3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
        11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
         7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
        15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

    G_vMaxBitSize          = [ ...
        1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
         144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
        1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
        1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];
end

%----------------------------------------------------------
% functions
%----------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% lENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

%----------------------------------------------------------
% Variables
%----------------------------------------------------------
span = 4;
sps = 3;
impulse_response_of_gmsk = gaussdesign(RX_BT,span,sps);
%[impulse_response_of_gmsk, len_gmsk]        = gmsk_impulse_response(RX_BT, OSR, LEN_PSF, 3);
RX_GMSK_BT_0_5_FIR_N = length(impulse_response_of_gmsk);

G_vRxRawDataBuff = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData = struct('nPntX', uint8(0), ...
                    'dSumX', uint32(DC_MID_LEVEL * RX_PRE_MAX_BUF_SIZE), ...
                    'dCntX', uint16(0), ...
                    'wAvrX', uint16(DC_MID_LEVEL), ...
                    'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData = struct('wDotPattern', uint16(0), ...
                    'wDotChanged', uint8(0), ...
                    'wDotCountX', uint8(0));
G_wRxShiftReg    = 0;
G_dSwRxPllCntrX  = 0;
G_dSwRxPllSampP  = 0;
G_dSwRxPllSampC  = 0;
G_wRxCurrBitD    = 0;
G_wRxPrevBitD    = 0;
G_wCrcRegData    = 0;
G_wRxBitCount    = 0;

G_wRxAfAdcData   = 0;
G_wRxNrziCntr    = 0;
G_wRxNrziCurr    = 0;
G_wRxNrziPrev    = 0;
G_wRxNrziTemp    = 0;
G_wRxReferValue  = DC_MID_LEVEL;
G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue  = 0;
G_dRxAdcErrCnt   = 0;

G_wNewBitData    = 0;
G_bRxByteData    = 0;

G_PreStart       = 0;
G_PreOffset      = 120;

G_dSyncDetCnt    = 0;
G_dAdcErrCnt     = 0;
G_dStartErrCnt   = 0;
G_dPloadErrCnt   = 0;
G_dStuffErrCnt   = 0;
G_dCrcErrCnt     = 0;
G_dRcvPktCnt     = 0;

G_dRxAfAdcSumVal = 0;
G_dRxAfAdcCntVal = 0;

ENABLE_NOTCH_FLT   = 0;
ENABLE_GMSK_RX_FLT = 1;

ENABLE_PLOT1     = 0;
ENABLE_PLOT2     = 0;
ENABLE_PLOT3     = 0;

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
G_hDumpFile = fopen('./DumpData/AisDumpData_ch1.bin');
G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');

for nSmpCnt = 1:length(G_pSrcDataCh1)
    if (ENABLE_NOTCH_FLT == 1)
        rX = G_pSrcDataCh1(nSmpCnt);
        rY = G_vNotchDataX(1) + NOTCH_FLT_B(1) * rX;
        G_vNotchDataX(1) = (NOTCH_FLT_B(2) * rX) + (NOTCH_FLT_A(1) * rY) + G_vNotchDataX(2);
        G_vNotchDataX(2) = (NOTCH_FLT_B(3) * rX) + (NOTCH_FLT_A(2) * rY) + G_vNotchDataX(3);
        G_wRxAfAdcData = floor(rY);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nSmpCnt);
    end

    G_vRxRawDataBuff(1:1) = [];
    G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = floor(G_wRxAfAdcData);

    if (ENABLE_GMSK_RX_FLT == 1)
        %{
        nSum = 0;
        for idx = 1:RX_GMSK_BT_0_5_FIR_N
            nSum = nSum + (G_vRxRawDataBuff(idx) * G_vRxGmskCoefficient(idx));
        end
        G_wRxAfAdcData = floor(nSum / RX_GMSK_MAX_DATA_VALUE);
        %}
        %{
        conv_data = conv(G_vRxRawDataBuff, G_vRxGmskCoefficient);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N) / RX_GMSK_MAX_DATA_VALUE);
        %}
        
        conv_data = conv(G_vRxRawDataBuff, impulse_response_of_gmsk);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N));

        conv_data = conv(conv_data, impulse_response_of_gmsk);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N));

        G_pFilteredData(nSmpCnt) = G_wRxAfAdcData;
    end

    %%%%%if (G_wRxAfAdcData < DC_MIN_LEVEL || G_wRxAfAdcData > DC_MAX_LEVEL)
    if (G_pSrcDataCh1(nSmpCnt) < DC_MIN_LEVEL || G_pSrcDataCh1(nSmpCnt) > DC_MAX_LEVEL)
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = DC_MID_LEVEL * RX_PRE_MAX_BUF_SIZE;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
            G_dRxAdcErrCnt = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > 20)
                G_dRxAdcErrCnt = 0;

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;
            
                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    else
        if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
            G_xPreData.nPntX = (G_xPreData.nPntX + 1);
            index = G_xPreData.nPntX;
            if (G_xPreData.nPntX >= RX_PRE_MAX_BUF_SIZE)
                G_xPreData.nPntX  = 0;
            end

            if(G_xPreData.dCntX >= RX_PRE_MAX_BUF_SIZE)
                G_xPreData.dSumX = G_xPreData.dSumX - G_xPreData.vData(index);
            else
                G_xPreData.dSumX = G_xPreData.dSumX - DC_MID_LEVEL;
                G_xPreData.dCntX = G_xPreData.dCntX + 1;
            end

            G_xPreData.dSumX = G_xPreData.dSumX + G_wRxAfAdcData;
            G_xPreData.wAvrX = G_xPreData.dSumX / RX_PRE_MAX_BUF_SIZE;
            G_xPreData.vData(index) = G_wRxAfAdcData;
    
            G_wRxReferValue = G_xPreData.wAvrX;
        end
    end

    if (G_wRxAfAdcData > G_wRxReferValue)
        G_wRxNrziCurr = 1;
    else
        G_wRxNrziCurr = 0;
    end

    %%%%% ProcessRxDataSwPllRun()
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        %%%%% ProcessRxStatusPreamble();
        G_dRxAfAdcSumVal = G_dRxAfAdcSumVal + G_wRxAfAdcData;
        G_dRxAfAdcCntVal = G_dRxAfAdcCntVal + 1;

        if (G_wRxNrziPrev == G_wRxNrziCurr)
            G_xDotData.wDotCountX = G_xDotData.wDotCountX + 1;
        else
            %%%%%if(G_xDotData.wDotCountX < (OSR * 2 - 3) || G_xDotData.wDotCountX > (OSR * 2 + 3))
            if(G_xDotData.wDotCountX < (OSR * 2 - 3) || G_xDotData.wDotCountX > (OSR * 2 + 3))
                if(G_xDotData.wDotChanged)
                    G_xDotData.wDotPattern = 0;
                    G_xDotData.wDotChanged = 0;
                end

                G_xDotData.wDotCountX = 1;
                G_dRxAfAdcSumVal = G_wRxAfAdcData;
                G_dRxAfAdcCntVal = 1;
            else
                if(G_wRxNrziPrev)
                    G_xDotData.wDotPattern = bitor(G_xDotData.wDotPattern, 1);
                end

                G_xDotData.wDotPattern = bitand(G_xDotData.wDotPattern, uint16(RX_DOT_MAX_CNT_MASK));
                if (G_xDotData.wDotPattern ~= 0x0001 && ...
                    G_xDotData.wDotPattern ~= 0x0002 && ...
                    G_xDotData.wDotPattern ~= 0x0005 && ...
                    G_xDotData.wDotPattern ~= 0x000a && ...
                    G_xDotData.wDotPattern ~= 0x0015 && ...
                    G_xDotData.wDotPattern ~= 0x002a && ...
                    G_xDotData.wDotPattern ~= 0x0055)
                    if (G_xDotData.wDotPattern)
                        G_xDotData.wDotPattern = 0;
                        G_xDotData.wDotChanged = 0;
                    end
                    G_xDotData.wDotCountX = 1;
                    G_dRxAfAdcSumVal = G_wRxAfAdcData;
                    G_dRxAfAdcCntVal = 1;
                else
                    if(G_xDotData.wDotPattern == RX_DOT_START_P_MASK)
                        G_dRxAfAdcSumVal = G_wRxAfAdcData;
                        G_dRxAfAdcCntVal = 1;
                    end

                    G_xDotData.wDotChanged = 1;

                    if(G_xDotData.wDotPattern == RX_DOT_DETCT_P_MASK)
                        if (ENABLE_PLOT1 == 1)
                            figure(1);
                            if (nSmpCnt > G_PreOffset)
                                G_PreStart = nSmpCnt-G_PreOffset;
                            else
                                G_PreStart = 1;
                            end
                            x1 = G_PreStart:nSmpCnt+800;
                            x2 = G_PreStart:nSmpCnt;
                            subplot(2,1,1); plot(x1, G_pSrcDataCh1(G_PreStart:nSmpCnt+800), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('detected\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                            subplot(2,1,2); plot(x2, G_pSrcDataCh1(G_PreStart:nSmpCnt), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('filtered\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                        end

                        G_wRxReferValue = floor(G_dRxAfAdcSumVal / G_dRxAfAdcCntVal);
                        %%%G_wRxReferValue = floor(G_dRxAfAdcSumVal / G_dRxAfAdcCntVal) - 10;

                        G_wRxRunStatus  = RX_MDM_STATUS_START;
                        G_wRxShiftReg   = 0;
                        G_wRxBitCount   = 0;
                        G_wRxPrevBitD   = G_wRxNrziPrev;
                        G_wBitSamplCntr = 1;
                        G_dRxAdcErrCnt  = 0;

                        G_dSwRxPllValue = RX_PLL_HALF;
                        G_dSwRxPllCntrX = 1;
                        G_dSwRxPllSampC = G_wRxNrziPrev;
                        G_dSwRxPllSampP = G_wRxNrziPrev;

                        G_xPreData.nPntX = 0;
                        G_xPreData.dSumX = DC_MID_LEVEL * RX_PRE_MAX_BUF_SIZE;
                        G_xPreData.dCntX = 0;
                        G_xPreData.wAvrX = DC_MID_LEVEL;

                        G_xDotData.wDotPattern = 0;
                        G_xDotData.wDotChanged = 0;
                        G_xDotData.wDotCountX = 0;

                        G_dRxAfAdcSumVal = 0;
                        G_dRxAfAdcCntVal = 0;

                        G_dSyncDetCnt = G_dSyncDetCnt + 1;
                    else
                        G_xDotData.wDotPattern = bitshift(G_xDotData.wDotPattern, 1);
                        G_xDotData.wDotCountX = 1;
                    end
                end
            end
        end

        %if (G_wRxNrziCurr ~= G_wRxNrziPrev)
        %    G_wRxNrziCntr = 1;
        %else
        %    G_wRxNrziCntr = G_wRxNrziCntr + 1;
        %end

        G_wRxNrziPrev = G_wRxNrziCurr;
        G_dSwRxPllSampP = G_wRxNrziCurr;
    else
        G_dSwRxPllSampC = G_wRxNrziCurr;

        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        % 아래 코드 사용시 수신율이 높아짐.
        if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
            if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
            %if(G_wRxNrziCntr <= (OSR - 2))
                G_wRxNrziCntr = G_wRxNrziCntr + 1;
                G_dSwRxPllSampC = G_dSwRxPllSampP;
            else
                G_wRxNrziCntr = 1;
            end
        else
            G_wRxNrziCntr = G_wRxNrziCntr + 1;
        end
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

        if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
            if (G_wRxRunStatus == RX_MDM_STATUS_START)
                if (G_dSwRxPllCntrX >= (OSR * 2 - 2) && G_dSwRxPllCntrX <= (OSR * 2 + 2))
                    G_dSwRxPllValue = RX_PLL_HALF + RX_PLL_STEP;
                end
            end
    
            if (G_dSwRxPllValue < RX_PLL_HALF)
                G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
            else
                G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
            end

            G_dSwRxPllCntrX = 1;
        else
            G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
        end

        G_dSwRxPllSampP = G_dSwRxPllSampC;

        G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
        if(G_dSwRxPllValue >= RX_PLL_FULL)
            G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
        else
            continue;
        end

        G_wRxCurrBitD = G_dSwRxPllSampC;

        %%%%% ProcessRxDataCommonRun()
        G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
        if (G_wRxCurrBitD == G_wRxPrevBitD)
            G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
        else
            G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
        end

        G_wRxPrevBitD = G_wRxCurrBitD;

        switch (G_wRxRunStatus)
            case RX_MDM_STATUS_START
                if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    if (ENABLE_PLOT2 == 1)
                        figure(2);
                        x1 = G_PreStart:nSmpCnt+800;
                        x2 = G_PreStart:nSmpCnt;
                        subplot(2,1,1); plot(x1, G_pSrcDataCh1(G_PreStart:nSmpCnt+800), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('detected\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                        subplot(2,1,2); plot(x2, G_pSrcDataCh1(G_PreStart:nSmpCnt), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('filtered\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                    end

                    %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                    %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();
                    G_wRxBitCount    = 0;
                    G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                else
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if(G_wRxBitCount >= 32)
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dStartErrCnt = G_dStartErrCnt + 1;
                    end
                end

            case RX_MDM_STATUS_PRELOAD
                G_wRxBitCount = G_wRxBitCount + 1;
                if (G_wRxBitCount == 8)
                    G_wRxBitCount = 0;
                    G_wCrcRegData = 0xffff;
                    G_wRxRunStatus= RX_MDM_STATUS_DATA;
                    %ClrRxRawFormTemp();

                    nTemp = bitshift(G_wRxShiftReg, 2);
                    nTemp = bitand(nTemp, 0x00ff);
                    nMsgID = G_vReverDataTableX(nTemp + 1);
                    if (nMsgID < 0)
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dPloadErrCnt = G_dPloadErrCnt + 1;
                    else
                        m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                    end
                end

            case RX_MDM_STATUS_DATA
                if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if(G_wRxBitCount >= (m_wRxMaxBitSize))
                        %%%%%% ResetToRxStatusPreamble()
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_bRxByteData = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dStuffErrCnt = G_dStuffErrCnt + 1;
                        continue;
                    end

                    G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                    G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));

                    %if(bitand(G_wRxBitCount, 0x07) == 0)
                    %    PutDataIntoRxRawBuff(G_bRxByteData);
                    %end

                    if (bitand(bitxor(G_wCrcRegData, G_wNewBitData), 0x0001))           % Pass new bit through CRC calculation
                        G_wCrcRegData = bitxor(bitshift(G_wCrcRegData, -1), 0x8408);   % Xor with the CRC polynomial (X^16 + X^12 + X^5 + 1)
                    else
                        G_wCrcRegData = bitshift(G_wCrcRegData, -1);
                    end
                end

                if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    if (ENABLE_PLOT3 == 1)
                        figure(3);
                        x1 = G_PreStart:nSmpCnt+50;
                        x2 = G_PreStart:nSmpCnt;
                        plot(x1, G_pSrcDataCh1(G_PreStart:nSmpCnt+50), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('filtered\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                    end

                    if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                        %WritePacketIntoRxRawBuff();

                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_bRxByteData = 0;
                        G_dRcvPktCnt = G_dRcvPktCnt + 1;
                    else
                        %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                        %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                        G_wRxBitCount    = 0;
                        G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                        %G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                        G_bRxByteData    = 0;
                        G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                    end
                end

            otherwise
                warning('Unexpected run status.');
        end
    end
end

figure(9);
%subplot(2,1,1); plot(kkk, detected_data_seq_100(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_100(kkk), 'k-d', kkk, 0.5*bit_error_100(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
%subplot(2,1,2); 
bar_x = ["SyncDet" "AdcErr" "StartErr" "PloadErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dPloadErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y);
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')

%{
%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
fileID = fopen('./DumpData/rst.bin');
source_data = fread(fileID, 'uint16'); %
for i = 1:length(source_data)
    if source_data(i) == 1
        source_data(i) = 1;
    else
        source_data(i) = -1;
    end
end

%-------------------------------------------------------------------------
dot_pattern       = repmat([1, 1, -1, -1], 1, 6);   % Dot pattern
start_pattern     = [-1, -1, -1, -1, -1, -1, -1, 1];% Start pattern
preamble          = [dot_pattern, start_pattern]';  % Preamble
preamble_os       = repelem(preamble, OSR);         % Over sampled preamble
LEN_DOT_PATTERN   = length(dot_pattern);            % Length of dot pattern
LEN_START_PATTERN = length(start_pattern);          % Length of start pattern
LEN_PREAMBLE      = length(preamble);               % Length of preamble
LEN_PREAMBLE_OS   = LEN_PREAMBLE*OSR;               % Length of over sampled preamble

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [data_seq_00] = mlsd (rx_data, ...
                          source_data, ...
                          h0, ...
                          h1, ...
                          LOCAL_RANGE_ONE_PERIOD_BAUD_RATE)

    % expected signal values taking isi into acount
    h_000 = -h1 - h0 - h1;
    h_001 = -h1 - h0 + h1;
    h_010 = -h1 + h0 - h1;
    h_011 = -h1 + h0 + h1;
    h_100 = h1 - h0 - h1;
    h_101 = h1 - h0 + h1;
    h_110 = h1 + h0 - h1;
    h_111 = h1 + h0 + h1;

    INF = 1e2;

    pm_00_init = INF;
    pm_01_init = INF;
    pm_10_init = 0;   % initially at state 10
    pm_11_init = INF;

    pm_00_old = [];
    pm_01_old = [];
    pm_10_old = [];
    pm_11_old = [];
    data_seq_00_old = [];
    data_seq_01_old = [];
    data_seq_10_old = [];
    data_seq_11_old = [];
    state_seq_00_old = [];
    state_seq_01_old = [];
    state_seq_10_old = [];
    state_seq_11_old = [];


    for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE        %LOCAL_RANGE_WITH_VALID_DATA
        % state 00 (decimal = 0)
        bm_00_0(k) = (rx_data(k) - h_000)^2;        % branch metric at state 00 when 0 comes in
        bm_10_0(k) = (rx_data(k) - h_100)^2;        % branch metric at state 10 when 0 comes in
        if k == 1
            pm_cand1 = pm_00_init + bm_00_0(k);
            pm_cand2 = pm_10_init + bm_10_0(k);
        else
            pm_cand1 = pm_00_old(end) + bm_00_0(k); % path metric candidate 1 at state 00 when 0 comes in
            pm_cand2 = pm_10_old(end) + bm_10_0(k); % path metric candidate 2 at state 00 when 0 comes in
        end
        if pm_cand1 < pm_cand2
            pm_00 = [pm_00_old; pm_cand1];          % path metric at state 00
            data_seq_00 = [data_seq_00_old; -1];    % sequence up to state 00
            state_seq_00 = [state_seq_00_old; 0];   % sequence up to state 00
        else
            pm_00 = [pm_00_old; pm_cand2];          % path metric at state 00
            data_seq_00 = [data_seq_10_old; -1];    % sequence up to state 00
            state_seq_00 = [state_seq_10_old; 0];   % sequence up to state 00
        end

        % state 01 (decimal = 1)
        bm_00_1(k) = (rx_data(k) - h_001)^2;
        bm_10_1(k) = (rx_data(k) - h_101)^2;
        if k == 1
            pm_cand1 = pm_00_init + bm_00_1(k);     % Path metric candidate 1
            pm_cand2 = pm_10_init + bm_10_1(k);     % Path metric candidate 2
        else
            pm_cand1 = pm_00_old(end) + bm_00_1(k); % Path metric candidate 1
            pm_cand2 = pm_10_old(end) + bm_10_1(k); % path metric candidate 2
        end
        if pm_cand1 < pm_cand2
            pm_01 = [pm_01_old; pm_cand1];
            data_seq_01 = [data_seq_00_old; 1];
            state_seq_01 = [state_seq_00_old; 1];
        else
            pm_01 = [pm_01_old; pm_cand2];
            data_seq_01 = [data_seq_10_old; 1];
            state_seq_01 = [state_seq_10_old; 1];
        end


        % state 10 (decimal = 2)
        bm_01_0(k) = (rx_data(k) - h_010)^2;
        bm_11_0(k) = (rx_data(k) - h_110)^2;
        if k == 1
            pm_cand1 = pm_01_init + bm_01_0(k);     % Path metric candidate 1
            pm_cand2 = pm_11_init + bm_11_0(k);     % Path metric candidate 2
        else
            pm_cand1 = pm_01_old(end) + bm_01_0(k); % path metric candidate 1
            pm_cand2 = pm_11_old(end) + bm_11_0(k); % path metric candidate 2
        end
        if pm_cand1 < pm_cand2
            pm_10 = [pm_10_old; pm_cand1];
            data_seq_10 = [data_seq_01_old; -1];
            state_seq_10 = [state_seq_01_old; 2];
        else
            pm_10 = [pm_10_old; pm_cand2];
            data_seq_10 = [data_seq_11_old; -1];
            state_seq_10 = [state_seq_11_old; 2];
        end

        % state 11 (decimal = 3)
        bm_01_1(k) = (rx_data(k) - h_011)^2;
        bm_11_1(k) = (rx_data(k) - h_111)^2;
        if k == 1
            pm_cand1 = pm_01_init + bm_01_1(k);     % path metric candidate 1
            pm_cand2 = pm_11_init + bm_11_1(k);     % path metric candidate 2
        else
            pm_cand1 = pm_01_old(end) + bm_01_1(k); % path metric candidate 1
            pm_cand2 = pm_11_old(end) + bm_11_1(k); % path metric candidate 2
        end
        if pm_cand1 < pm_cand2
            pm_11 = [pm_11_old; pm_cand1];
            data_seq_11 = [data_seq_01_old; 1];
            state_seq_11 = [state_seq_01_old; 3];
        else
            pm_11 = [pm_11_old; pm_cand2];
            data_seq_11 = [data_seq_11_old; 1];
            state_seq_11 = [state_seq_11_old; 3];
        end

        pm_00_old = pm_00;
        pm_01_old = pm_01;
        pm_10_old = pm_10;
        pm_11_old = pm_11;
        data_seq_00_old = data_seq_00;
        data_seq_01_old = data_seq_01;
        data_seq_10_old = data_seq_10;
        data_seq_11_old = data_seq_11;
        state_seq_00_old = state_seq_00;
        state_seq_01_old = state_seq_01;
        state_seq_10_old = state_seq_10;
        state_seq_11_old = state_seq_11;
    end

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    % for plotting after packet period finished
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    if (1)
        for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
            if rx_data(k+1) > 0 % note (k+1) since rx_data(1) is not valid one
                direct_decision(k) = 1;
            else
                direct_decision(k) = -1;
            end
        end
        for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
            if data_seq_00(k) ~= source_data(k)
                bit_error(k) = 1;
            else
                bit_error(k) = 0;
            end
        end
        for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
            if direct_decision(k) ~= source_data(k)
                bit_error_simple(k) = -1;
            else
                bit_error_simple(k) = 0;
            end
        end

        figure(100);
        kkk = 1:length(pm_00);
        subplot(5,1,1); plot(kkk, rx_data(kkk+1), '-x'); title('rx\_data'); grid;
        subplot(5,1,2); plot(kkk, data_seq_00(kkk), '-x', kkk, source_data(kkk), '-o', kkk, 0.5*bit_error_simple(kkk), 'k-d', kkk, 0.5*bit_error(kkk), 'b-d'); title('estimated data sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
        subplot(5,1,3); plot(kkk, data_seq_00(kkk), '-x', kkk, data_seq_01(kkk), '-+', kkk, data_seq_10(kkk), '-o', kkk, data_seq_11(kkk), '-d'); title('data sequence at 00 (x), at 01 (+), at 10 (o), at 11 (d)'); grid;
        subplot(5,1,4); plot(kkk, state_seq_00(kkk), '-x', kkk, state_seq_01(kkk), '-+', kkk, state_seq_10(kkk), '-o', kkk, state_seq_11(kkk), '-d'); title('state sequence at 00 (x), at 01 (+), at 10 (o), at 11 (d)'); grid;
        subplot(5,1,5); plot(kkk, pm_00 (kkk), '-x', kkk, pm_01 (kkk), '-+', kkk, pm_10 (kkk), '-o', kkk, pm_11 (kkk), '-d'); title('path metric at 00 (x), at 01 (+), at 10 (o), at 11 (d)'); grid;

        figure(101);
        subplot(4,1,1); plot(kkk, bm_00_0(kkk), '-x', kkk, bm_10_0(kkk), '-o'); title('branch metric at 00 (x), at 10 (o) with input 0, to move to 00'); grid;
        subplot(4,1,2); plot(kkk, bm_00_1(kkk), '-x', kkk, bm_10_1(kkk), '-o'); title('branch metric at 00 (x), at 10 (o) with input 1, to move to 01'); grid;
        subplot(4,1,3); plot(kkk, bm_01_0(kkk), '-x', kkk, bm_11_0(kkk), '-o'); title('branch metric at 01 (x), at 11 (o) with input 0, to move to 10'); grid;
        subplot(4,1,4); plot(kkk, bm_01_1(kkk), '-x', kkk, bm_11_1(kkk), '-o'); title('branch metric at 01 (x), at 11 (o) with input 1, to move to 11'); grid;
    end
end

% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% lENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gaussfilter_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2 + 1):(LENGTH / 2)) / OSR;
    h = 1 * sqrt(2 * pi / log(2)) * exp(-2 / log(2) * (BT * pi * t).^2);
    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

function [h0, bias] = calc_h0_and_bias (received_preamble, source_preamble, h1_to_h0_ratio)
    LEN_PREAMBLE = length(source_preamble);
    A = [];
    for k = 2 : LEN_PREAMBLE - 1
        consecutive_3_bits = [source_preamble(k-1), source_preamble(k), source_preamble(k+1)];
        if consecutive_3_bits == [1 1 1]
            A = [A; [(1 + 2*h1_to_h0_ratio), 1]];
        elseif consecutive_3_bits == [-1 -1 -1]
            A = [A; [(-1 - 2*h1_to_h0_ratio), 1]];
        elseif (norm(consecutive_3_bits - [1 1 -1]) == 0) || (norm(consecutive_3_bits - [-1 1 1]) == 0)
            A = [A; [(1), 1]];
        elseif (norm(consecutive_3_bits - [1 -1 -1]) == 0) || (norm(consecutive_3_bits - [-1 -1 1]) == 0)
            A = [A; [(-1), 1]];
        elseif consecutive_3_bits == [-1 1 -1]
            A = [A; [(1 - 2*h1_to_h0_ratio), 1]];
        elseif consecutive_3_bits == [1 -1 1]
            A = [A; [(-1 + 2*h1_to_h0_ratio), 1]];
        end
    end
    coeff_vector = pinv(A)*received_preamble(2:LEN_PREAMBLE - 1);
    h0 = coeff_vector(1);
    bias = coeff_vector(2);
%     A
%     h0
%     bias
end



function [h0, bias] = calc_h0_and_bias_2 (received_preamble_os, source_preamble_os, half_impulse_response_os, FILTERED_TWICE)

    if FILTERED_TWICE
        INDEX_START = 39;
    else
        INDEX_START = 20;
    end

    A = [source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), ones(length(received_preamble_os),1)]; % 39 means the time point which matches the first sample of received preamble_os

    coeff_vector = pinv(A)*received_preamble_os;
    h0 = coeff_vector(1)*max(half_impulse_response_os);
    bias = coeff_vector(2);

    figure(34)
    kkk = 1:length(received_preamble_os);
    scale = h0/max(half_impulse_response_os);
    subplot(3,1,1); plot(received_preamble_os); grid; title('received\_preamble\_os');
    subplot(3,1,2); plot(source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)); grid; title('source\_preamble\_os');
    subplot(3,1,3); plot(kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), '-o', kkk, received_preamble_os, '-+'); grid; title('source\_preamble\_os (o), received\_preamble\_os (x)');
end

function half_impulse_response_os = calc_half_impulse_response_os_of_convolved_filter (filter_kind1, BT1, filter_kind2, BT2, OSR, HALF_LENTH, NORM)
    if filter_kind1 == 1 % gmsk
        [h1, k1] = gmsk_impulse_response (BT1, OSR, 2*HALF_LENTH - 1, NORM);
    elseif filter_kind1 == 2 % gaussfilter
        [h1, k1] = gaussfilter_impulse_response(BT1, OSR, 2*HALF_LENTH - 1, NORM);
    end
    if filter_kind2 == 1 % gmsk
        [h2, k2] = gmsk_impulse_response (BT2, OSR, 2*HALF_LENTH - 1, NORM);
    elseif filter_kind2 == 2 % gaussfilter
        [h2, k2] = gaussfilter_impulse_response(BT2, OSR, 2*HALF_LENTH - 1, NORM);
    end
    h_conv = conv (h1, h2);
    index_max = find (h_conv == max(h_conv));
    if NORM == 1
        h_conv = h_conv/max(h_conv);
    elseif NORM == 2
        h_conv = h_conv/norm(h_conv);
    end
    half_impulse_response_os = h_conv (index_max : (index_max + HALF_LENTH - 1));

    figure(111);
    kkk = 1:2*HALF_LENTH - 1;
    subplot(3,1,1); plot(kkk, h1, '-o', kkk, h2, '-x'); grid; title('h1 (o), h2 (x)');
    subplot(3,1,2); plot(h_conv, '-o'); grid; title('h\_conv');
    subplot(3,1,3); plot(half_impulse_response_os, '-o'); grid; title('half\_impulse\_response\_os');
end


%-----------------------------------------------------------
% impulse response of gmsk, gauss filter
%-----------------------------------------------------------
[impulse_response_of_gmsk, kkk]        = gmsk_impulse_response(BT, OSR, LEN_PSF, 2);
[impulse_response_of_gaussfilter, kkk] = gaussfilter_impulse_response(BT, OSR, LEN_PSF, 2);
impulse_response_of_gmsk_twice         = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);

preamble_zero_padded                   = upsample (preamble, OSR);
preamble_filtered_by_gmsk              = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gaussfilter       = conv (preamble_zero_padded, impulse_response_of_gaussfilter);
preamble_filtered_by_gmsk_twice        = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

source_data_zero_padded                = upsample (source_data, OSR);
source_data_filtered_by_gmsk           = conv (source_data_zero_padded, impulse_response_of_gmsk);
source_data_filtered_by_gaussfilter    = conv (source_data_zero_padded, impulse_response_of_gaussfilter);
source_data_filtered_by_gmsk_twice     = conv (source_data_zero_padded, impulse_response_of_gmsk_twice);

preamble_range    = 1:length(preamble_filtered_by_gmsk);
source_data_range = 1:length(source_data_filtered_by_gmsk);

figure('Name', 'impulse response and preamble');
subplot(6,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response');
subplot(6,1,2); plot(preamble, '-o'); grid; title('preamble');
subplot(6,1,3); plot(preamble_range, preamble_filtered_by_gmsk(preamble_range), '-o'); grid; title('preamble\_filtered');
subplot(6,1,4); plot(source_data, '-o'); grid; title('source\_data');
subplot(6,1,5); plot(source_data_range, source_data_filtered_by_gmsk(source_data_range), '-o'); grid; title('source\_data\_filtered');

figure('Name', 'gmsk and gauss filter');
subplot(5,1,1); plot(kkk, impulse_response_of_gmsk, '-o', kkk, impulse_response_of_gaussfilter, '-x');
                    grid; title('impulse\_response (gmsk) (o), (gaussfilter) (x)');
subplot(5,1,2); plot(preamble, '-o'); grid; title('preamble');
subplot(5,1,3); plot(preamble_range, preamble_filtered_by_gmsk, '-o', preamble_range, preamble_filtered_by_gaussfilter, '-x');
                    grid; title('preamble\_filtered (gmsk) (o), (gaussfitler) (x)');
subplot(5,1,4); plot(source_data, '-o'); grid; title('source\_data');
subplot(5,1,5); plot(source_data_range, source_data_filtered_by_gmsk, '-o', source_data_range, source_data_filtered_by_gaussfilter, '-x');
                    grid; title('source\_data\_filtered (gmsk) (o), (gaussfitler) (x)');


%-----------------------------------------------------------
% filtered data by gmsk with noise
%-----------------------------------------------------------
source_data_filtered_by_gmsk_repeated = repmat([zeros(100,1); source_data_filtered_by_gmsk], NUM_OF_PACKETS+1, 1);
LEN1 = length(source_data_filtered_by_gmsk_repeated);
source_data_filtered_by_gmsk_repeated_100 = DC_LEVEL + AMP*(source_data_filtered_by_gmsk_repeated + STD_100*randn(LEN1,1));
source_data_filtered_by_gmsk_repeated_110 = DC_LEVEL + AMP*(source_data_filtered_by_gmsk_repeated + STD_110*randn(LEN1,1));
source_data_filtered_by_gmsk_repeated_115 = DC_LEVEL + AMP*(source_data_filtered_by_gmsk_repeated + STD_115*randn(LEN1,1));

if (USE_RX_FILTER == 1)
    x_100 = conv(impulse_response_of_gmsk, source_data_filtered_by_gmsk_repeated_100);
    x_110 = conv(impulse_response_of_gmsk, source_data_filtered_by_gmsk_repeated_110);
    x_115 = conv(impulse_response_of_gmsk, source_data_filtered_by_gmsk_repeated_115);
else
    x_100 = source_data_filtered_by_gmsk_repeated_100;
    x_110 = source_data_filtered_by_gmsk_repeated_110;
    x_115 = source_data_filtered_by_gmsk_repeated_115;
end

figure('Name', 'filtered data by gmsk with noise');
subplot(7,1,1); plot(source_data_filtered_by_gmsk_repeated, '-o'); grid; title('source\_data\_filtered\_by\_gmsk\_repeated');
subplot(7,1,2); plot(source_data_filtered_by_gmsk_repeated_100, '-'); grid; title('x\_100 (tx filtered)');
subplot(7,1,3); plot(source_data_filtered_by_gmsk_repeated_110, '-'); grid; title('x\_110 (tx filtered)');
subplot(7,1,4); plot(source_data_filtered_by_gmsk_repeated_115, '-'); grid; title('x\_115 (tx filtered)');
subplot(7,1,5); plot(x_100, '-'); grid; title('x\_100 (tx + rx filtered)');
subplot(7,1,6); plot(x_110, '-'); grid; title('x\_110 (tx + rx filtered)');
subplot(7,1,7); plot(x_115, '-'); grid; title('x\_115 (tx + rx filtered)');


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
LEN_RAWDATA = length(x_100);
x_100_cen = zeros(LEN_RAWDATA, 1);
x_110_cen = zeros(LEN_RAWDATA, 1);
x_115_cen = zeros(LEN_RAWDATA, 1);
dc_level_100 = zeros(LEN_RAWDATA, 1);
dc_level_110 = zeros(LEN_RAWDATA, 1);
dc_level_115 = zeros(LEN_RAWDATA, 1);
dc_level_fixed_100 = zeros(LEN_RAWDATA, 1);
dc_level_fixed_110 = zeros(LEN_RAWDATA, 1);
dc_level_fixed_115 = zeros(LEN_RAWDATA, 1);
r_100 = zeros(PKT_PERIOD_OS, 1);
r_110 = zeros(PKT_PERIOD_OS, 1);
r_115 = zeros(PKT_PERIOD_OS, 1);
sum_error_total = [];
sum_error1_total = [];

for p = 1:NUM_OF_PACKETS

    GLOBAL_RANGE_ONE_PERIOD = ((p - 1)*PKT_PERIOD_OS + 1) : ((p - 1)*PKT_PERIOD_OS + PKT_PERIOD_OS);
    GLOBAL_RANGE_TWO_PERIODS = ((p - 1)*PKT_PERIOD_OS + 1) : ((p - 1)*PKT_PERIOD_OS + 2*PKT_PERIOD_OS);
    LOCAL_RANGE_ONE_PERIOD = 1 : PKT_PERIOD_OS;
    LOCAL_RANGE_TWO_PERIODS = 1 : 2*PKT_PERIOD_OS;
    LOCAL_RANGE_WITH_VALID_DATA = 1 : length(source_data_filtered_by_gmsk);
    LOCAL_RANGE_ONE_PERIOD_BAUD_RATE = 1 : PKT_PERIOD;

    % dc levels

    sum_x_100 = 0;
    sum_x_110 = 0;
    sum_x_115 = 0;
    for j = LOCAL_RANGE_ONE_PERIOD
        k = GLOBAL_RANGE_ONE_PERIOD(j);
        if j <= LEN_DOT_PATTERN*OSR
            sum_x_100 = sum_x_100 + x_100(k);
            sum_x_110 = sum_x_110 + x_110(k);
            sum_x_115 = sum_x_115 + x_115(k);
        else
            sum_x_100 = sum_x_100 + x_100(k) - x_100(k - LEN_DOT_PATTERN*OSR);
            sum_x_110 = sum_x_110 + x_110(k) - x_110(k - LEN_DOT_PATTERN*OSR);
            sum_x_115 = sum_x_115 + x_115(k) - x_115(k - LEN_DOT_PATTERN*OSR);
        end
        dc_level_100(k) = sum_x_100 / (LEN_DOT_PATTERN*OSR);
        dc_level_110(k) = sum_x_110 / (LEN_DOT_PATTERN*OSR);
        dc_level_115(k) = sum_x_115 / (LEN_DOT_PATTERN*OSR);
    end


    % correlations

    tmp_100 = [];
    tmp_110 = [];
    tmp_115 = [];
    for j = LOCAL_RANGE_ONE_PERIOD
        k = GLOBAL_RANGE_ONE_PERIOD(j);
        if j <= LEN_PREAMBLE*OSR
            correl_100(k) = 0;
            correl_110(k) = 0;
            correl_115(k) = 0;
        else
            tmp_100 = x_100(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level_100(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_110 = x_110(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level_110(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_115 = x_115(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level_115(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_100 = tmp_100/norm(tmp_100);
            tmp_110 = tmp_110/norm(tmp_110);
            tmp_115 = tmp_115/norm(tmp_115);
            correl_100(k) = (tmp_100)' * preamble_os / norm(preamble_os);
            correl_110(k) = (tmp_110)' * preamble_os / norm(preamble_os);
            correl_115(k) = (tmp_115)' * preamble_os / norm(preamble_os);
        end
    end
    correl_100(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correl_100(GLOBAL_RANGE_ONE_PERIOD));
    correl_110(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correl_110(GLOBAL_RANGE_ONE_PERIOD));
    correl_115(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correl_115(GLOBAL_RANGE_ONE_PERIOD));

    % dc_level_fixed

    k_max_100(p) = GLOBAL_RANGE_ONE_PERIOD (find(correl_100(GLOBAL_RANGE_ONE_PERIOD) == max(correl_100(GLOBAL_RANGE_ONE_PERIOD))));
    k_max_110(p) = GLOBAL_RANGE_ONE_PERIOD (find(correl_110(GLOBAL_RANGE_ONE_PERIOD) == max(correl_110(GLOBAL_RANGE_ONE_PERIOD))));
    k_max_115(p) = GLOBAL_RANGE_ONE_PERIOD (find(correl_115(GLOBAL_RANGE_ONE_PERIOD) == max(correl_115(GLOBAL_RANGE_ONE_PERIOD))));

    DC_LEVEL_FIXED_100 = dc_level_100(k_max_100(p) - (LEN_START_PATTERN+1)*OSR);
    DC_LEVEL_FIXED_110 = dc_level_110(k_max_110(p) - (LEN_START_PATTERN+1)*OSR);
    DC_LEVEL_FIXED_115 = dc_level_115(k_max_115(p) - (LEN_START_PATTERN+1)*OSR);

    for j = LOCAL_RANGE_TWO_PERIODS
        k = GLOBAL_RANGE_TWO_PERIODS(j);
        dc_level_fixed_100(k) = DC_LEVEL_FIXED_100;
        dc_level_fixed_110(k) = DC_LEVEL_FIXED_110;
        dc_level_fixed_115(k) = DC_LEVEL_FIXED_115;
        x_100_cen(k) = x_100(k) - dc_level_fixed_100(k);
        x_110_cen(k) = x_110(k) - dc_level_fixed_110(k);
        x_115_cen(k) = x_115(k) - dc_level_fixed_115(k);
    end


    %-----------------------------------------------------------
    %   data input in this packet
    %-----------------------------------------------------------

    TIMING_OFFSET = -2; %-2;
    k_start_100(p) = k_max_100(p) + TIMING_OFFSET + 1*OSR; % TIMING_OFFSET = -2     ؼ         preamble bit timing   ã   1*OSR       ؼ         preamble bit timing    ã
    k_start_110(p) = k_max_110(p) + TIMING_OFFSET + 1*OSR; % TIMING_OFFSET = -2
    k_start_115(p) = k_max_115(p) + TIMING_OFFSET + 1*OSR; % TIMING_OFFSET = -2
%     k_start_100(p) = k_max_100(p) + TIMING_OFFSET; % TIMING_OFFSET = -2
%     k_start_110(p) = k_max_110(p) + TIMING_OFFSET; % TIMING_OFFSET = -2
%     k_start_115(p) = k_max_115(p) + TIMING_OFFSET; % TIMING_OFFSET = -2
%     k_start_100(p) = k_max_100(p) + (-LEN_PREAMBLE + 1)*OSR + TIMING_OFFSET; % -2
%     k_start_110(p) = k_max_110(p) + (-LEN_PREAMBLE + 1)*OSR + TIMING_OFFSET; % -2
%     k_start_115(p) = k_max_115(p) + (-LEN_PREAMBLE + 1)*OSR + TIMING_OFFSET; % -2

    for j = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        r_100(j) = (1/100)*x_100_cen (k_start_100(p) + (j - 1)*OSR);
        r_110(j) = (1/100)*x_110_cen (k_start_110(p) + (j - 1)*OSR);
        r_115(j) = (1/100)*x_115_cen (k_start_115(p) + (j - 1)*OSR);
    end
    rx_data_of_last_preamble_bit_100 = (1/100)*x_100_cen (k_start_100(p) + (-1)*OSR);
    rx_data_of_last_preamble_bit_110 = (1/100)*x_110_cen (k_start_110(p) + (-1)*OSR);
    rx_data_of_last_preamble_bit_115 = (1/100)*x_115_cen (k_start_115(p) + (-1)*OSR);

    received_preamble_100 = zeros(LEN_PREAMBLE,1);
    received_preamble_110 = zeros(LEN_PREAMBLE,1);
    received_preamble_115 = zeros(LEN_PREAMBLE,1);
    for j = 1:LEN_PREAMBLE
        received_preamble_100(j) = (1/100)*x_100_cen (k_start_100(p) - LEN_PREAMBLE*OSR + (j - 1)*OSR);
        received_preamble_110(j) = (1/100)*x_110_cen (k_start_110(p) - LEN_PREAMBLE*OSR + (j - 1)*OSR);
        received_preamble_115(j) = (1/100)*x_115_cen (k_start_115(p) - LEN_PREAMBLE*OSR + (j - 1)*OSR);
    end

    received_preamble_os_100 = zeros(LEN_PREAMBLE_OS,1);
    received_preamble_os_110 = zeros(LEN_PREAMBLE_OS,1);
    received_preamble_os_115 = zeros(LEN_PREAMBLE_OS,1);
    for j = 1:LEN_PREAMBLE_OS
        received_preamble_os_100(j) = (1/100)*x_100_cen (k_start_100(p) - LEN_PREAMBLE*OSR + (j - 1)*1);
        received_preamble_os_110(j) = (1/100)*x_110_cen (k_start_110(p) - LEN_PREAMBLE*OSR + (j - 1)*1);
        received_preamble_os_115(j) = (1/100)*x_115_cen (k_start_115(p) - LEN_PREAMBLE*OSR + (j - 1)*1);
    end

    if (USE_RX_FILTER == 1)
        index_max = find(impulse_response_of_gmsk_twice == max(impulse_response_of_gmsk_twice));
        half_impulse_response_os = impulse_response_of_gmsk_twice(index_max:(index_max+2*OSR));
        [h0_100, bias_100] = calc_h0_and_bias_2 (received_preamble_os_100, preamble_filtered_by_gmsk_twice, half_impulse_response_os, 1); % FILTERED_TWICE = 1
        [h0_110, bias_110] = calc_h0_and_bias_2 (received_preamble_os_110, preamble_filtered_by_gmsk_twice, half_impulse_response_os, 1);
        [h0_115, bias_115] = calc_h0_and_bias_2 (received_preamble_os_115, preamble_filtered_by_gmsk_twice, half_impulse_response_os, 1);
    else
        index_max = find(impulse_response_of_gmsk == max(impulse_response_of_gmsk));
        half_impulse_response_os = impulse_response_of_gmsk(index_max:(index_max+2*OSR));
        [h0_100, bias_100] = calc_h0_and_bias_2 (received_preamble_os_100, preamble_filtered_by_gmsk, half_impulse_response_os, 0); % FILTERED_TWICE = 0
        [h0_110, bias_110] = calc_h0_and_bias_2 (received_preamble_os_110, preamble_filtered_by_gmsk, half_impulse_response_os, 0);
        [h0_115, bias_115] = calc_h0_and_bias_2 (received_preamble_os_115, preamble_filtered_by_gmsk, half_impulse_response_os, 0);
    end




%    rx_data = r_100; % r_100 r_110 r_115
%     BT = 0.4;
%     RX_LEVEL = 1; %1.05;

    source_data1 = [source_data(LEN_PREAMBLE+1:end); zeros(100,1)]; % source_data (229 bits)    preamble (32 bits)       κ  (197 bits) + zero stuffing


    HALF_LENTH = fix(LEN_PSF/2);
    half_impulse_response_os = calc_half_impulse_response_os_of_convolved_filter (1, BT, 1, BT, OSR, HALF_LENTH, 1);
    h0_100_for_mlsd = half_impulse_response_os(1)*h0_100;
    h1_100_for_mlsd = half_impulse_response_os(OSR+1)*h0_100;
    h0_110_for_mlsd = half_impulse_response_os(1)*h0_110;
    h1_110_for_mlsd = half_impulse_response_os(OSR+1)*h0_110;
    h0_115_for_mlsd = half_impulse_response_os(1)*h0_115;
    h1_115_for_mlsd = half_impulse_response_os(OSR+1)*h0_115;


    detected_data_seq_100 = mlsd ([rx_data_of_last_preamble_bit_100; r_100], ...
                                source_data1, ...
                                h0_100_for_mlsd, ...
                                h1_100_for_mlsd, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE);

    detected_data_seq_110 = mlsd ([rx_data_of_last_preamble_bit_110; r_110], ...
                                source_data1, ...
                                h0_110_for_mlsd, ...
                                h1_110_for_mlsd, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE);

    detected_data_seq_115 = mlsd ([rx_data_of_last_preamble_bit_115; r_115], ...
                                source_data1, ...
                                h0_115_for_mlsd, ...
                                h1_115_for_mlsd, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE);



    for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE %LOCAL_RANGE_WITH_VALID_DATA
        if r_100(k) > 0
            direct_decision_100(k) = 1;
        else
            direct_decision_100(k) = -1;
        end
        if r_110(k) > 0
            direct_decision_110(k) = 1;
        else
            direct_decision_110(k) = -1;
        end
        if r_115(k) > 0
            direct_decision_115(k) = 1;
        else
            direct_decision_115(k) = -1;
        end
    end

    for i = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if detected_data_seq_100(i) ~= source_data1(i)
            bit_error_100(i) = 1;
        else
            bit_error_100(i) = 0;
        end
        if detected_data_seq_110(i) ~= source_data1(i)
            bit_error_110(i) = 1;
        else
            bit_error_110(i) = 0;
        end
        if detected_data_seq_115(i) ~= source_data1(i)
            bit_error_115(i) = 1;
        else
            bit_error_115(i) = 0;
        end
    end

    for i = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if direct_decision_100(i) ~= source_data1(i)
            bit_error_simple_100(i) = -1;
        else
            bit_error_simple_100(i) = 0;
        end
        if direct_decision_110(i) ~= source_data1(i)
            bit_error_simple_110(i) = -1;
        else
            bit_error_simple_110(i) = 0;
        end
        if direct_decision_115(i) ~= source_data1(i)
            bit_error_simple_115(i) = -1;
        else
            bit_error_simple_115(i) = 0;
        end
    end

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    figure(201);
    tmp1 = GLOBAL_RANGE_ONE_PERIOD;
    tmp2 = GLOBAL_RANGE_TWO_PERIODS;
    tmp3 = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE;
    subplot(6,1,1); plot(tmp2, x_100(tmp2), '-+', tmp2, dc_level_100(tmp2), '--', tmp2, dc_level_fixed_100(tmp2), '--', tmp1, correl_100(tmp1), 'r-'); title('correlation (-100 db)'); grid;
    subplot(6,1,2); plot(tmp3, r_100(tmp3), '-+'); title('rx\_data (-100 db)'); grid; %ylim([-6 6]);
    subplot(6,1,3); plot(tmp2, x_110(tmp2), '-+', tmp2, dc_level_110(tmp2), '--', tmp2, dc_level_fixed_110(tmp2), '--', tmp1, correl_110(tmp1), 'r-'); title('correlation (-110 db)'); grid;
    subplot(6,1,4); plot(tmp3, r_110(tmp3), '-+'); title('rx\_data (-110 db)'); grid; %ylim([-6 6]);
    subplot(6,1,5); plot(tmp2, x_115(tmp2), '-+', tmp2, dc_level_115(tmp2), '--', tmp2, dc_level_fixed_115(tmp2), '--', tmp1, correl_115(tmp1), 'r-'); title('correlation (-115 db)'); grid;
    subplot(6,1,6); plot(tmp3, r_115(tmp3), '-+'); title('rx\_data (-115 db)'); grid; %ylim([-6 6]);

    figure(202);
    kkk = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE;
    subplot(6,1,1); plot(kkk, r_100(kkk), '-x'); title('rx\_data (100)'); grid; %ylim([-6 6]);
    subplot(6,1,2); plot(kkk, detected_data_seq_100(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_100(kkk), 'k-d', kkk, 0.5*bit_error_100(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
    subplot(6,1,3); plot(kkk, r_110(kkk), '-x'); title('rx\_data (110)'); grid; %ylim([-6 6]);
    subplot(6,1,4); plot(kkk, detected_data_seq_110(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_110(kkk), 'k-d', kkk, 0.5*bit_error_110(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
    subplot(6,1,5); plot(kkk, r_115(kkk), '-x'); title('rx\_data (115)'); grid; %ylim([-6 6]);
    subplot(6,1,6); plot(kkk, detected_data_seq_115(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_115(kkk), 'k-d', kkk, 0.5*bit_error_115(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;

    k_max = [k_max_100(p), k_max_110(p), k_max_115(p)]
    %pause;
end

%}
