clear all;

%pkg load signal
%pkg load coder

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
OSR = 5;                % Over sampling rate
BT = 0.4;               % Bandwidth time

PACKET_PERIOD = 60;    % Packet period
PACKET_PERIOD_OS = PACKET_PERIOD*OSR;
LEN_PSF = 8*OSR;        % Pulse shaping filter length

%H_NORMALIZATION = 2; % 1: normalized by h_max, 2: normalized by norm(h)
APPLY_RX_FILTER = 1;
AMP = 200;              % Amplitude
DC_LEVEL = 0;           % DC level
NUM_OF_PACKETS = 1;     % Number of packets

SNR_DB_100 = 25;        % SNR in dB 

SNR_DB_110 = SNR_DB_100 - 10;
SNR_DB_115 = SNR_DB_100 - 15;
STD_100 = sqrt(10^(-SNR_DB_100/10));
STD_110 = sqrt(10^(-SNR_DB_110/10));
STD_115 = sqrt(10^(-SNR_DB_115/10));

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
fileID = fopen('rst.bin');
source_data = fread(fileID, 'uint16');
for i = 1:length(source_data)
    if source_data(i) == 1
        source_data(i) = 1;
    else
        source_data(i) = -1;
    end
end

%-------------------------------------------------------------------------
dot_pattern       = repmat([1, 1, -1, -1], 1, 6);   % Dot pattern
start_pattern     = [-1, -1, -1, -1, -1, -1, -1, 1];%[1, 1, 1, 1, 1, 1, 1, -1];      % Start pattern
preamble          = [dot_pattern, start_pattern]';  % Preamble
preamble_os       = repelem(preamble, OSR);         % Over sampled preamble
LEN_DOT_PATTERN   = length(dot_pattern);            % Length of dot pattern
LEN_START_PATTERN = length(start_pattern);          % Length of start pattern
LEN_PREAMBLE      = length(preamble);               % Length of preamble
LEN_PREAMBLE_OS   = LEN_PREAMBLE*OSR;               % Length of over sampled preamble

%----------------------------------------------------------
% functions
%----------------------------------------------------------
function [data_seq_00] = mlsd ( rx_data, ...
                                h0, ...
                                h1, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE)

    % ISI를 고려한 예상 신호 값
    h_values = [-h1 - h0 - h1, -h1 - h0 + h1, -h1 + h0 - h1, -h1 + h0 + h1, ...
                 h1 - h0 - h1,  h1 - h0 + h1,  h1 + h0 - h1,  h1 + h0 + h1];

    INF = 1e5;
    %          00   01   10   11
    pm_init = [INF,  0, INF, INF]; %Start bit의 마지막은 01이므로 0으로 세팅하고 나머지는 무한대

    data_seq_00_old = [];
    data_seq_01_old = [];
    data_seq_10_old = [];
    data_seq_11_old = [];

    for (k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE)      %LOCAL_RANGE_WITH_VALID_DATA
        % state 00 (decimal = 0)
        bm_00_0(k) = (rx_data(k) - h_values(1))^2;  % branch metric at state 00 when 0 comes in
        bm_10_0(k) = (rx_data(k) - h_values(5))^2;  % branch metric at state 10 when 0 comes in
        if (k == 1)
            pm_cand1 = pm_init(1) + bm_00_0(k);
            pm_cand2 = pm_init(3) + bm_10_0(k);
        else
            pm_cand1 = pm_00_old + bm_00_0(k);      % path metric candidate 1 at state 00 when 0 comes in
            pm_cand2 = pm_10_old + bm_10_0(k);      % path metric candidate 2 at state 00 when 0 comes in
        end
        if (pm_cand1 < pm_cand2)
            pm_00 = pm_cand1;                       % path metric at state 00
            data_seq_00 = [data_seq_00_old; -1];    % sequence up to state 00
        else
            pm_00 = pm_cand2;                       % path metric at state 00
            data_seq_00 = [data_seq_10_old; -1];    % sequence up to state 00
        end

        % state 01 (decimal = 1)
        bm_00_1(k) = (rx_data(k) - h_values(2))^2;
        bm_10_1(k) = (rx_data(k) - h_values(6))^2;
        if (k == 1)
            pm_cand1 = pm_init(1) + bm_00_1(k);     % Path metric candidate 1
            pm_cand2 = pm_init(3) + bm_10_1(k);     % Path metric candidate 2
        else
            pm_cand1 = pm_00_old + bm_00_1(k);      % Path metric candidate 1
            pm_cand2 = pm_10_old + bm_10_1(k);      % path metric candidate 2
        end
        if (pm_cand1 < pm_cand2)
            pm_01 = pm_cand1;
            data_seq_01 = [data_seq_00_old; 1];
        else
            pm_01 = pm_cand2;
            data_seq_01 = [data_seq_10_old; 1];
        end

        % state 10 (decimal = 2)
        bm_01_0(k) = (rx_data(k) - h_values(3))^2;
        bm_11_0(k) = (rx_data(k) - h_values(7))^2;
        if (k == 1)
            pm_cand1 = pm_init(2) + bm_01_0(k);     % Path metric candidate 1
            pm_cand2 = pm_init(4) + bm_11_0(k);     % Path metric candidate 2
        else
            pm_cand1 = pm_01_old + bm_01_0(k);      % path metric candidate 1
            pm_cand2 = pm_11_old + bm_11_0(k);      % path metric candidate 2
        end
        if (pm_cand1 < pm_cand2)
            pm_10 = pm_cand1;
            data_seq_10 = [data_seq_01_old; -1];
        else
            pm_10 = pm_cand2;
            data_seq_10 = [data_seq_11_old; -1];
        end

        % state 11 (decimal = 3)
        bm_01_1(k) = (rx_data(k) - h_values(4))^2;
        bm_11_1(k) = (rx_data(k) - h_values(8))^2;
        if (k == 1)
            pm_cand1 = pm_init(2) + bm_01_1(k);     % path metric candidate 1
            pm_cand2 = pm_init(4) + bm_11_1(k);     % path metric candidate 2
        else
            pm_cand1 = pm_01_old + bm_01_1(k);      % path metric candidate 1
            pm_cand2 = pm_11_old + bm_11_1(k);      % path metric candidate 2
        end
        if (pm_cand1 < pm_cand2)
            pm_11 = pm_cand1;
            data_seq_11 = [data_seq_01_old; 1];
        else
            pm_11 = pm_cand2;
            data_seq_11 = [data_seq_11_old; 1];
        end

        pm_00_old = pm_00;
        pm_01_old = pm_01;
        pm_10_old = pm_10;
        pm_11_old = pm_11;
        data_seq_00_old = data_seq_00;
        data_seq_01_old = data_seq_01;
        data_seq_10_old = data_seq_10;
        data_seq_11_old = data_seq_11;
    end
end


function [h, k] = calc_impulse_response_of_gmsk (BT, OSR, LENGTH, H_NORMALIZATION)
% h: impulse response
% k: time indeces
    h = zeros(LENGTH, 1);
    k = (-LENGTH/2+1):(LENGTH/2);
    Ts = 1;
    h = 0.5*(erf(pi*BT*sqrt(2/log(2))*(k/OSR + 0.5)) - erf(pi*BT*sqrt(2/log(2))*(k/OSR - 0.5)));

    if H_NORMALIZATION == 1
        h = h/max(h);
    elseif H_NORMALIZATION == 2
        h = h/norm(h);
    end
end


function [h, k] = calc_impulse_response_of_gaussfilter (BT, OSR, LENGTH, H_NORMALIZATION)
% h: impulse response
% k: time indeces
%    BT = 0.4;
    h = zeros(LENGTH, 1);
    k = (-LENGTH/2+1):(LENGTH/2); %-3:3;
    Ts = 1;
    T = OSR; %2;
    h = 1*sqrt(2*pi/log(2))*exp(-2/log(2)*(BT*pi*(k/T)).^2); % from [svedek]
    if H_NORMALIZATION == 1
        h = h/max(h);
    elseif H_NORMALIZATION == 2
        h = h/norm(h);
    end
end


function h = calc_impulse_response_of_raised_cosine_pulse (ALPHA, OSR, LENGTH)
% h: impulse response
% k: time indeces
%    ALPHA = 0.5;
    h = zeros(LENGTH, 1);
    for i = 1:LENGTH
        ii = i - LENGTH/2;
        if ii == 0
            h(i) = 1;
        elseif mod(abs(ii), OSR) == 0
            h(i) = 0;
        else
            h(i) = sin(pi*ii/OSR)/(pi*ii/OSR)*cos(ALPHA*pi*ii/OSR)/(1 - 4*ALPHA^2*(ii/OSR)^2); % from [gitlin]
        end
    end
end

function [h0, bias] = calc_h0_and_bias (received_preamble, source_preamble, h1_to_h0_ratio)
    LEN_PREAMBLE = length(source_preamble);
    A = [];
    for k = 2 : LEN_PREAMBLE - 1
        consecutive_3_bits = [source_preamble(k-1), source_preamble(k), source_preamble(k+1)];
        if consecutive_3_bits == [1 1 1]
            A = [A; [(1 + 2*h1_to_h0_ratio), 1]];
        elseif consecutive_3_bits == [-1 -1 -1]
            A = [A; [(-1 - 2*h1_to_h0_ratio), 1]];
        elseif (norm(consecutive_3_bits - [1 1 -1]) == 0) || (norm(consecutive_3_bits - [-1 1 1]) == 0)
            A = [A; [(1), 1]];
        elseif (norm(consecutive_3_bits - [1 -1 -1]) == 0) || (norm(consecutive_3_bits - [-1 -1 1]) == 0)
            A = [A; [(-1), 1]];
        elseif consecutive_3_bits == [-1 1 -1]
            A = [A; [(1 - 2*h1_to_h0_ratio), 1]];
        elseif consecutive_3_bits == [1 -1 1]
            A = [A; [(-1 + 2*h1_to_h0_ratio), 1]];
        end
    end
    coeff_vector = pinv(A)*received_preamble(2:LEN_PREAMBLE - 1);
    h0 = coeff_vector(1);
    bias = coeff_vector(2);
%     A
%     h0
%     bias
end



function [h0, bias] = calc_h0_and_bias_2 (received_preamble_os, source_preamble_os, half_impulse_response_os, FILTERED_TWICE)

    if FILTERED_TWICE
        INDEX_START = 39;
    else
        INDEX_START = 20;
    end

    A = [source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), ones(length(received_preamble_os),1)]; % 39 means the time point which matches the first sample of received preamble_os

    pinv_data = pinv(A);
    coeff_vector = pinv_data*received_preamble_os;
    h0 = coeff_vector(1)*max(half_impulse_response_os);
    bias = coeff_vector(2);

    figure(34)
    kkk = 1:length(received_preamble_os);
    scale = h0/max(half_impulse_response_os);
    subplot(4,1,1); plot(received_preamble_os); grid; title('received\_preamble\_os');
    subplot(4,1,2); plot(source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)); grid; title('source\_preamble\_os');
    subplot(4,1,3); plot(kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1), '-o', kkk, scale*source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1)+bias, '-x', kkk, received_preamble_os, '-+'); grid; title('source\_preamble\_os (o), received\_preamble\_os (x)');
    subplot(4,1,4); plot(kkk, pinv_data(1,:), '-o', kkk, pinv_data(2,:), '-x'); grid; title('test');
end



% 
function half_impulse_response_os = calc_half_impulse_response_os (filter_kind, BT, OSR, HALF_LENTH)
    if (filter_kind == 1)       % gmsk
        [h, k] = calc_impulse_reponse_of_gmsk_1 (BT, OSR, 2*HALF_LENTH - 1);
        index_max = find (h == max(h));
        half_impulse_response_os = h (index_max : (index_max + HALF_LENTH - 1));
    elseif (filter_kind == 2)   % gaussfilter
        [h, k] = calc_impulse_reponse_of_gaussfilter_1 (BT, OSR, 2*HALF_LENTH - 1);
        index_max = find (h == max(h));
        half_impulse_response_os = h (index_max : (index_max + HALF_LENTH - 1));
    end
end

function half_impulse_response_os = calc_half_impulse_response_os_of_convolved_filter (filter_kind1, BT1, filter_kind2, BT2, OSR, HALF_LENTH, H_NORMALIZATION)
    if filter_kind1 == 1 % gmsk
        [h1, k1] = calc_impulse_response_of_gmsk (BT1, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    elseif filter_kind1 == 2 % gaussfilter
        [h1, k1] = calc_impulse_response_of_gaussfilter (BT1, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    end
    if filter_kind2 == 1 % gmsk
        [h2, k2] = calc_impulse_response_of_gmsk (BT2, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    elseif filter_kind2 == 2 % gaussfilter
        [h2, k2] = calc_impulse_response_of_gaussfilter (BT2, OSR, 2*HALF_LENTH - 1, H_NORMALIZATION);
    end
    h_conv = conv (h1, h2);
    index_max = find (h_conv == max(h_conv));
    if H_NORMALIZATION == 1
        h_conv = h_conv/max(h_conv);
    elseif H_NORMALIZATION == 2
        h_conv = h_conv/norm(h_conv);
    end
    half_impulse_response_os = h_conv (index_max : (index_max + HALF_LENTH - 1));

    figure(111);
    kkk = 1:2*HALF_LENTH - 1;
    subplot(3,1,1); plot(kkk, h1, '-o', kkk, h2, '-x'); grid; title('h1 (o), h2 (x)');
    subplot(3,1,2); plot(h_conv, '-o'); grid; title('h\_conv');
    subplot(3,1,3); plot(half_impulse_response_os, '-o'); grid; title('half\_impulse\_response\_os');
end


%-----------------------------------------------------------
% impulse response of gmsk, gauss filter
%-----------------------------------------------------------
[impulse_response_of_gmsk, kkk]        = calc_impulse_response_of_gmsk (BT, OSR, LEN_PSF, 2);
[impulse_response_of_gaussfilter, kkk] = calc_impulse_response_of_gaussfilter (BT, OSR, LEN_PSF, 2);
impulse_response_of_gmsk_twice         = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);

preamble_zero_padded                   = upsample (preamble, OSR);
preamble_filtered_by_gmsk              = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gaussfilter       = conv (preamble_zero_padded, impulse_response_of_gaussfilter);
preamble_filtered_by_gmsk_twice        = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

source_data_zero_padded                = upsample (source_data, OSR);
source_data_filtered_by_gmsk           = conv (source_data_zero_padded, impulse_response_of_gmsk);
source_data_filtered_by_gaussfilter    = conv (source_data_zero_padded, impulse_response_of_gaussfilter);
source_data_filtered_by_gmsk_twice     = conv (source_data_zero_padded, impulse_response_of_gmsk_twice);

preamble_range    = 1:length(preamble_filtered_by_gmsk);
source_data_range = 1:length(source_data_filtered_by_gmsk);

figure('Name', 'impulse response and preamble');
subplot(6,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk)');
subplot(6,1,2); plot(impulse_response_of_gmsk_twice, '-o'); grid; title('impulse\_response (gmsk twice) (x)');
subplot(6,1,3); plot(preamble, '-o'); grid; title('preamble');
subplot(6,1,4); plot(preamble_range, preamble_filtered_by_gmsk(preamble_range), '-o', ...
                    preamble_range, preamble_filtered_by_gmsk_twice(preamble_range), '-x');
                    grid; title('preamble\_filtered (gmsk) (o), (gmsk twice) (x)');
subplot(6,1,5); plot(source_data, '-o'); grid; title('source\_data');
subplot(6,1,6); plot(source_data_range, source_data_filtered_by_gmsk(source_data_range), '-o', ...
                    source_data_range, source_data_filtered_by_gmsk_twice(source_data_range), '-x');
                    grid; title('source\_data\_filtered (gmsk) (o), (gmsk twice) (x)');

figure('Name', 'gmsk and gauss filter');
subplot(5,1,1); plot(kkk, impulse_response_of_gmsk, '-o', kkk, impulse_response_of_gaussfilter, '-x');
                    grid; title('impulse\_response (gmsk) (o), (gaussfilter) (x)');
subplot(5,1,2); plot(preamble, '-o'); grid; title('preamble');
subplot(5,1,3); plot(preamble_range, preamble_filtered_by_gmsk, '-o', preamble_range, preamble_filtered_by_gaussfilter, '-x');
                    grid; title('preamble\_filtered (gmsk) (o), (gaussfitler) (x)');
subplot(5,1,4); plot(source_data, '-o'); grid; title('source\_data');
subplot(5,1,5); plot(source_data_range, source_data_filtered_by_gmsk, '-o', source_data_range, source_data_filtered_by_gaussfilter, '-x');
                    grid; title('source\_data\_filtered (gmsk) (o), (gaussfitler) (x)');


%-----------------------------------------------------------
% filtered data by gmsk with noise
%-----------------------------------------------------------
source_data_filtered_by_gmsk_repeated = repmat([zeros(100,1); source_data_filtered_by_gmsk], NUM_OF_PACKETS+1, 1);
LEN1 = length(source_data_filtered_by_gmsk_repeated);
source_data_filtered_by_gmsk_repeated_100 = DC_LEVEL + AMP*(source_data_filtered_by_gmsk_repeated + STD_100*randn(LEN1,1));
source_data_filtered_by_gmsk_repeated_110 = DC_LEVEL + AMP*(source_data_filtered_by_gmsk_repeated + STD_110*randn(LEN1,1));
source_data_filtered_by_gmsk_repeated_115 = DC_LEVEL + AMP*(source_data_filtered_by_gmsk_repeated + STD_115*randn(LEN1,1));

if APPLY_RX_FILTER == 1
    x_100 = conv(impulse_response_of_gmsk, source_data_filtered_by_gmsk_repeated_100);
    x_110 = conv(impulse_response_of_gmsk, source_data_filtered_by_gmsk_repeated_110);
    x_115 = conv(impulse_response_of_gmsk, source_data_filtered_by_gmsk_repeated_115);
else
    x_100 = source_data_filtered_by_gmsk_repeated_100;
    x_110 = source_data_filtered_by_gmsk_repeated_110;
    x_115 = source_data_filtered_by_gmsk_repeated_115;
end

figure('Name', 'filtered data by gmsk with noise');
subplot(7,1,1); plot(source_data_filtered_by_gmsk_repeated, '-o'); grid; title('source\_data\_filtered\_by\_gmsk\_repeated');
subplot(7,1,2); plot(source_data_filtered_by_gmsk_repeated_100, '-'); grid; title('x\_100 (tx filtered)');
subplot(7,1,3); plot(source_data_filtered_by_gmsk_repeated_110, '-'); grid; title('x\_110 (tx filtered)');
subplot(7,1,4); plot(source_data_filtered_by_gmsk_repeated_115, '-'); grid; title('x\_115 (tx filtered)');
subplot(7,1,5); plot(x_100, '-'); grid; title('x\_100 (tx + rx filtered)');
subplot(7,1,6); plot(x_110, '-'); grid; title('x\_110 (tx + rx filtered)');
subplot(7,1,7); plot(x_115, '-'); grid; title('x\_115 (tx + rx filtered)');


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
LEN_RAWDATA = length(x_100);
x_100_cen = zeros(LEN_RAWDATA, 1);
x_110_cen = zeros(LEN_RAWDATA, 1);
x_115_cen = zeros(LEN_RAWDATA, 1);
dc_level_100 = zeros(LEN_RAWDATA, 1);
dc_level_110 = zeros(LEN_RAWDATA, 1);
dc_level_115 = zeros(LEN_RAWDATA, 1);
dc_level_fixed_100 = zeros(LEN_RAWDATA, 1);
dc_level_fixed_110 = zeros(LEN_RAWDATA, 1);
dc_level_fixed_115 = zeros(LEN_RAWDATA, 1);
r_100 = zeros(PACKET_PERIOD_OS, 1);
r_110 = zeros(PACKET_PERIOD_OS, 1);
r_115 = zeros(PACKET_PERIOD_OS, 1);
sum_error_total = [];
sum_error1_total = [];

for p = 1:NUM_OF_PACKETS
    GLOBAL_RANGE_ONE_PERIOD = ((p - 1)*PACKET_PERIOD_OS + 1) : ((p - 1)*PACKET_PERIOD_OS + PACKET_PERIOD_OS);
    GLOBAL_RANGE_TWO_PERIODS = ((p - 1)*PACKET_PERIOD_OS + 1) : ((p - 1)*PACKET_PERIOD_OS + 2*PACKET_PERIOD_OS);
    LOCAL_RANGE_ONE_PERIOD = 1 : PACKET_PERIOD_OS;
    LOCAL_RANGE_TWO_PERIODS = 1 : 2*PACKET_PERIOD_OS;
    LOCAL_RANGE_WITH_VALID_DATA = 1 : length(source_data_filtered_by_gmsk);
    LOCAL_RANGE_ONE_PERIOD_BAUD_RATE = 1 : PACKET_PERIOD;

    % dc levels
    sum_x_100 = 0;
    sum_x_110 = 0;
    sum_x_115 = 0;
    for j = LOCAL_RANGE_ONE_PERIOD
        k = GLOBAL_RANGE_ONE_PERIOD(j);
        if j <= LEN_DOT_PATTERN*OSR
            sum_x_100 = sum_x_100 + x_100(k);
            sum_x_110 = sum_x_110 + x_110(k);
            sum_x_115 = sum_x_115 + x_115(k);
        else
            sum_x_100 = sum_x_100 + x_100(k) - x_100(k - LEN_DOT_PATTERN*OSR);
            sum_x_110 = sum_x_110 + x_110(k) - x_110(k - LEN_DOT_PATTERN*OSR);
            sum_x_115 = sum_x_115 + x_115(k) - x_115(k - LEN_DOT_PATTERN*OSR);
        end
        dc_level_100(k) = sum_x_100 / (LEN_DOT_PATTERN*OSR);
        dc_level_110(k) = sum_x_110 / (LEN_DOT_PATTERN*OSR);
        dc_level_115(k) = sum_x_115 / (LEN_DOT_PATTERN*OSR);
    end

    % correlations
    tmp_100 = [];
    tmp_110 = [];
    tmp_115 = [];
    for j = LOCAL_RANGE_ONE_PERIOD
        k = GLOBAL_RANGE_ONE_PERIOD(j);
        if j <= LEN_PREAMBLE*OSR
            correl_100(k) = 0;
            correl_110(k) = 0;
            correl_115(k) = 0;
        else
            tmp_100 = x_100(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level_100(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_110 = x_110(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level_110(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_115 = x_115(k - LEN_PREAMBLE*OSR + 1 : k) - dc_level_115(k - LEN_PREAMBLE*OSR + 1 : k);
            tmp_100 = tmp_100/norm(tmp_100);
            tmp_110 = tmp_110/norm(tmp_110);
            tmp_115 = tmp_115/norm(tmp_115);
            correl_100(k) = (tmp_100)' * preamble_os / norm(preamble_os);
            correl_110(k) = (tmp_110)' * preamble_os / norm(preamble_os);
            correl_115(k) = (tmp_115)' * preamble_os / norm(preamble_os);
            
        end
    end
    correl_100(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correl_100(GLOBAL_RANGE_ONE_PERIOD));
    correl_110(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correl_110(GLOBAL_RANGE_ONE_PERIOD));
    correl_115(GLOBAL_RANGE_ONE_PERIOD) = 1000*abs(correl_115(GLOBAL_RANGE_ONE_PERIOD));

    % dc_level_fixed
    k_max_100(p) = GLOBAL_RANGE_ONE_PERIOD (find(correl_100(GLOBAL_RANGE_ONE_PERIOD) == max(correl_100(GLOBAL_RANGE_ONE_PERIOD))));
    k_max_110(p) = GLOBAL_RANGE_ONE_PERIOD (find(correl_110(GLOBAL_RANGE_ONE_PERIOD) == max(correl_110(GLOBAL_RANGE_ONE_PERIOD))));
    k_max_115(p) = GLOBAL_RANGE_ONE_PERIOD (find(correl_115(GLOBAL_RANGE_ONE_PERIOD) == max(correl_115(GLOBAL_RANGE_ONE_PERIOD))));

    DC_LEVEL_FIXED_100 = dc_level_100(k_max_100(p) - (LEN_START_PATTERN+1)*OSR);
    DC_LEVEL_FIXED_110 = dc_level_110(k_max_110(p) - (LEN_START_PATTERN+1)*OSR);
    DC_LEVEL_FIXED_115 = dc_level_115(k_max_115(p) - (LEN_START_PATTERN+1)*OSR);

    for j = LOCAL_RANGE_TWO_PERIODS
        k = GLOBAL_RANGE_TWO_PERIODS(j);
        dc_level_fixed_100(k) = DC_LEVEL_FIXED_100;
        dc_level_fixed_110(k) = DC_LEVEL_FIXED_110;
        dc_level_fixed_115(k) = DC_LEVEL_FIXED_115;
        x_100_cen(k) = x_100(k) - dc_level_fixed_100(k);
        x_110_cen(k) = x_110(k) - dc_level_fixed_110(k);
        x_115_cen(k) = x_115(k) - dc_level_fixed_115(k);
    end


    %-----------------------------------------------------------
    %   data input in this packet
    %-----------------------------------------------------------

    TIMING_OFFSET = -2;
    k_start_100(p) = k_max_100(p) + TIMING_OFFSET + 1*OSR;
    k_start_110(p) = k_max_110(p) + TIMING_OFFSET + 1*OSR;
    k_start_115(p) = k_max_115(p) + TIMING_OFFSET + 1*OSR;

    for j = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        r_100(j) = x_100_cen (k_start_100(p) + (j - 1)*OSR);
        r_110(j) = x_110_cen (k_start_110(p) + (j - 1)*OSR);
        r_115(j) = x_115_cen (k_start_115(p) + (j - 1)*OSR);
    end
    rx_data_of_last_preamble_bit_100 = x_100_cen (k_start_100(p) + (-1)*OSR);
    rx_data_of_last_preamble_bit_110 = x_110_cen (k_start_110(p) + (-1)*OSR);
    rx_data_of_last_preamble_bit_115 = x_115_cen (k_start_115(p) + (-1)*OSR);

    received_preamble_100 = zeros(LEN_PREAMBLE,1);
    received_preamble_110 = zeros(LEN_PREAMBLE,1);
    received_preamble_115 = zeros(LEN_PREAMBLE,1);
    for j = 1:LEN_PREAMBLE
        received_preamble_100(j) = x_100_cen (k_start_100(p) - LEN_PREAMBLE*OSR + (j - 1)*OSR);
        received_preamble_110(j) = x_110_cen (k_start_110(p) - LEN_PREAMBLE*OSR + (j - 1)*OSR);
        received_preamble_115(j) = x_115_cen (k_start_115(p) - LEN_PREAMBLE*OSR + (j - 1)*OSR);
    end

    received_preamble_os_100 = zeros(LEN_PREAMBLE_OS,1);
    received_preamble_os_110 = zeros(LEN_PREAMBLE_OS,1);
    received_preamble_os_115 = zeros(LEN_PREAMBLE_OS,1);
    for j = 1:LEN_PREAMBLE_OS
        received_preamble_os_100(j) = x_100_cen (k_start_100(p) - LEN_PREAMBLE*OSR + (j - 1)*1);
        received_preamble_os_110(j) = x_110_cen (k_start_110(p) - LEN_PREAMBLE*OSR + (j - 1)*1);
        received_preamble_os_115(j) = x_115_cen (k_start_115(p) - LEN_PREAMBLE*OSR + (j - 1)*1);
    end

    if APPLY_RX_FILTER == 1
        index_max = find(impulse_response_of_gmsk_twice == max(impulse_response_of_gmsk_twice));
        half_impulse_response_os = impulse_response_of_gmsk_twice(index_max:(index_max+2*OSR));
        [h0_100, bias_100] = calc_h0_and_bias_2 (received_preamble_os_100, preamble_filtered_by_gmsk_twice, half_impulse_response_os, 1); % FILTERED_TWICE = 1
        [h0_110, bias_110] = calc_h0_and_bias_2 (received_preamble_os_110, preamble_filtered_by_gmsk_twice, half_impulse_response_os, 1);
        [h0_115, bias_115] = calc_h0_and_bias_2 (received_preamble_os_115, preamble_filtered_by_gmsk_twice, half_impulse_response_os, 1);
    else
        index_max = find(impulse_response_of_gmsk == max(impulse_response_of_gmsk));
        half_impulse_response_os = impulse_response_of_gmsk(index_max:(index_max+2*OSR));
        [h0_100, bias_100] = calc_h0_and_bias_2 (received_preamble_os_100, preamble_filtered_by_gmsk, half_impulse_response_os, 0); % FILTERED_TWICE = 0
        [h0_110, bias_110] = calc_h0_and_bias_2 (received_preamble_os_110, preamble_filtered_by_gmsk, half_impulse_response_os, 0);
        [h0_115, bias_115] = calc_h0_and_bias_2 (received_preamble_os_115, preamble_filtered_by_gmsk, half_impulse_response_os, 0);
    end


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

    PREAMBLE_PERIOD = 32;
    PREAMBLE_PERIOD_OS = PREAMBLE_PERIOD*OSR;
    PREAMBLE_PERIOD_EXTENDED = PREAMBLE_PERIOD + 3 - 2;
    PREAMBLE_PERIOD_EXTENDED_OS = PREAMBLE_PERIOD_EXTENDED*OSR
    LOCAL_RANGE_ONE_PERIOD_EXTENDED = 1 : PREAMBLE_PERIOD_EXTENDED;

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

    source_data1 = [source_data(LEN_PREAMBLE+1:end); zeros(100,1)]; % source_data (229 bits)    preamble (32 bits)       κ  (197 bits) + zero stuffing


    HALF_LENTH = fix(LEN_PSF/2);
    half_impulse_response_os = calc_half_impulse_response_os_of_convolved_filter (1, BT, 1, BT, OSR, HALF_LENTH, 1); % H_NORMALIZATION should be 1 (h_max = 1)
    h0_100_for_mlsd = half_impulse_response_os(1)*h0_100;
    h1_100_for_mlsd = half_impulse_response_os(OSR+1)*h0_100;
    h0_110_for_mlsd = half_impulse_response_os(1)*h0_110;
    h1_110_for_mlsd = half_impulse_response_os(OSR+1)*h0_110;
    h0_115_for_mlsd = half_impulse_response_os(1)*h0_115;
    h1_115_for_mlsd = half_impulse_response_os(OSR+1)*h0_115;


    detected_data_seq_100 = mlsd ([rx_data_of_last_preamble_bit_100; r_100], ...
                                h0_100_for_mlsd, ...
                                h1_100_for_mlsd, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE);

    detected_data_seq_110 = mlsd ([rx_data_of_last_preamble_bit_110; r_110], ...
                                h0_110_for_mlsd, ...
                                h1_110_for_mlsd, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE);

    detected_data_seq_115 = mlsd ([rx_data_of_last_preamble_bit_115; r_115], ...
                                h0_115_for_mlsd, ...
                                h1_115_for_mlsd, ...
                                LOCAL_RANGE_ONE_PERIOD_BAUD_RATE);

    for k = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if r_100(k) > 0
            direct_decision_100(k) = 1;
        else
            direct_decision_100(k) = -1;
        end
        if r_110(k) > 0
            direct_decision_110(k) = 1;
        else
            direct_decision_110(k) = -1;
        end
        if r_115(k) > 0
            direct_decision_115(k) = 1;
        else
            direct_decision_115(k) = -1;
        end
    end

    for i = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if detected_data_seq_100(i) ~= source_data1(i)
            bit_error_100(i) = 1;
        else
            bit_error_100(i) = 0;
        end
        if detected_data_seq_110(i) ~= source_data1(i)
            bit_error_110(i) = 1;
        else
            bit_error_110(i) = 0;
        end
        if detected_data_seq_115(i) ~= source_data1(i)
            bit_error_115(i) = 1;
        else
            bit_error_115(i) = 0;
        end
    end

    for i = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE
        if direct_decision_100(i) ~= source_data1(i)
            bit_error_simple_100(i) = -1;
        else
            bit_error_simple_100(i) = 0;
        end
        if direct_decision_110(i) ~= source_data1(i)
            bit_error_simple_110(i) = -1;
        else
            bit_error_simple_110(i) = 0;
        end
        if direct_decision_115(i) ~= source_data1(i)
            bit_error_simple_115(i) = -1;
        else
            bit_error_simple_115(i) = 0;
        end
    end

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    figure(201);
    tmp1 = GLOBAL_RANGE_ONE_PERIOD;
    tmp2 = GLOBAL_RANGE_TWO_PERIODS;
    tmp3 = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE;
    subplot(6,1,1); plot(tmp2, x_100(tmp2), '-+', tmp2, dc_level_100(tmp2), '--', tmp2, dc_level_fixed_100(tmp2), '--', tmp1, correl_100(tmp1), 'r-'); title('correlation (-100 db)'); grid;
    subplot(6,1,2); plot(tmp3, r_100(tmp3), '-+'); title('rx\_data (-100 db)'); grid; %ylim([-6 6]);
    subplot(6,1,3); plot(tmp2, x_110(tmp2), '-+', tmp2, dc_level_110(tmp2), '--', tmp2, dc_level_fixed_110(tmp2), '--', tmp1, correl_110(tmp1), 'r-'); title('correlation (-110 db)'); grid;
    subplot(6,1,4); plot(tmp3, r_110(tmp3), '-+'); title('rx\_data (-110 db)'); grid; %ylim([-6 6]);
    subplot(6,1,5); plot(tmp2, x_115(tmp2), '-+', tmp2, dc_level_115(tmp2), '--', tmp2, dc_level_fixed_115(tmp2), '--', tmp1, correl_115(tmp1), 'r-'); title('correlation (-115 db)'); grid;
    subplot(6,1,6); plot(tmp3, r_115(tmp3), '-+'); title('rx\_data (-115 db)'); grid; %ylim([-6 6]);

    figure(202);
    kkk = LOCAL_RANGE_ONE_PERIOD_BAUD_RATE;
    subplot(6,1,1); plot(kkk, r_100(kkk), '-x'); title('rx\_data (100)'); grid; %ylim([-6 6]);
    subplot(6,1,2); plot(kkk, detected_data_seq_100(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_100(kkk), 'k-d', kkk, 0.5*bit_error_100(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
    subplot(6,1,3); plot(kkk, r_110(kkk), '-x'); title('rx\_data (110)'); grid; %ylim([-6 6]);
    subplot(6,1,4); plot(kkk, detected_data_seq_110(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_110(kkk), 'k-d', kkk, 0.5*bit_error_110(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
    subplot(6,1,5); plot(kkk, r_115(kkk), '-x'); title('rx\_data (115)'); grid; %ylim([-6 6]);
    subplot(6,1,6); plot(kkk, detected_data_seq_115(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_115(kkk), 'k-d', kkk, 0.5*bit_error_115(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;

    k_max = [k_max_100(p), k_max_110(p), k_max_115(p)]
end
