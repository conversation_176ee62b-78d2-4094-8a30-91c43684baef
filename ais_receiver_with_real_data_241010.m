clear all;

%octave package
%pkg load signal
%pkg load coder

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA    = 0;            % 0: Ch1, 1: Ch2

ENABLE_NOTCH_FLT    = 0;
ENABLE_GMSK_RX_FLT  = 1;

ENABLE_PLOT1        = 1;
ENABLE_PLOT2        = 0;
ENABLE_PLOT3        = 0;


BIT_RATE = 9600;                    % Bit rate
OSR = 5;                            % Over sampling rate
BT = 0.4;                           % Transmit BT product
RX_BT = 0.5;                        % Receive BT product

PKT_PERIOD = 256;                   % Packet period
PKT_PERIOD_OS = PKT_PERIOD * OSR;
LEN_PSF = 8 * OSR;                  % Pulse shaping filter length

H_NORM = 2;                         % 1: normalized by h_max, 2: normalized by norm(h)
AMP = 200;                          % Amplitude
DC_LEVEL = 0;                       % DC level
NUM_OF_PACKETS = 1;                 % Number of packets

SNR_DB_100 = 25;                    % SNR in dB 

SNR_DB_110 = SNR_DB_100 - 10;
SNR_DB_115 = SNR_DB_100 - 15;
STD_100 = sqrt(10^(-SNR_DB_100 / 10));
STD_110 = sqrt(10^(-SNR_DB_110 / 10));
STD_115 = sqrt(10^(-SNR_DB_115 / 10));

USE_RX_FILTER = 1;

USE_LEGACY_MODEM = 1;
if (USE_LEGACY_MODEM == 1)
    % Legacy modem defines
    NOTCH_FLT_A  = [+1.999986841577810, -0.999986910116283];
    NOTCH_FLT_B  = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
    % BT=0.5
    G_vRxGmskCoefficient = [8, 187, 2427, 17821, 74016, 173924, 231226, 173924, 74016, 17821, 2427, 187, 8];

    DC_MIN_LEVEL = floor(  50 * 4095 / 3300);    % 0.05V
    DC_MID_LEVEL = floor(1000 * 4095 / 3300);    % 1.00V
    DC_MAX_LEVEL = floor(2000 * 4095 / 3300);    % 2.00V

    RX_PLL_FULL  = 2400;
    RX_PLL_HALF  = (RX_PLL_FULL / 2);
    RX_PLL_INCR  = (RX_PLL_FULL / OSR);
    RX_PLL_STEP  = (RX_PLL_INCR / 3);

    RX_GMSK_BT_0_4_FIR_N   = 17;
    RX_GMSK_BT_0_5_FIR_N   = 13;
    RX_GMSK_TO_INT_FACTOR  = 16;

    RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

    RX_MDM_STATUS_PREAMBLE = 0;
    RX_MDM_STATUS_START    = 1;
    RX_MDM_STATUS_PRELOAD  = 2;
    RX_MDM_STATUS_DATA     = 3;

    RX_DOT_MAX_CNT_SIZE    = 7;
    RX_DOT_MAX_CNT_MASK    = 0x7f;
    RX_DOT_START_P_MASK    = 0x05;
    RX_DOT_DETCT_P_MASK    = 0x55;
    RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

    RX_PRE_MAX_CNT_SIZE    = 12;
    RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

    G_vNotchDataX = zeros(1, 3);

    G_vReverDataTableX     = [ ...
     %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
         0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
         8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
         4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
        12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
         2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
        10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
         6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
        14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
         1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
         9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
         5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
        13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
         3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
        11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
         7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
        15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

    G_vMaxBitSize          = [ ...
        1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
         144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
        1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
        1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];
end

%----------------------------------------------------------
% functions
%----------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% lENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

%----------------------------------------------------------
% Variables
%----------------------------------------------------------
span = 4;
sps = 3;
impulse_response_of_gmsk = gaussdesign(RX_BT,span,sps);
%[impulse_response_of_gmsk, len_gmsk]        = gmsk_impulse_response(RX_BT, OSR, LEN_PSF, 3);
RX_GMSK_BT_0_5_FIR_N = length(impulse_response_of_gmsk);

G_vRxRawDataBuff = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData = struct('nPntX', uint8(0), ...
                    'dSumX', uint32(DC_MID_LEVEL * RX_PRE_MAX_BUF_SIZE), ...
                    'dCntX', uint16(0), ...
                    'wAvrX', uint16(DC_MID_LEVEL), ...
                    'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData = struct('wDotPattern', uint16(0), ...
                    'wDotChanged', uint8(0), ...
                    'wDotCountX', uint8(0));
G_wRxShiftReg    = 0;
G_dSwRxPllCntrX  = 0;
G_dSwRxPllSampP  = 0;
G_dSwRxPllSampC  = 0;
G_wRxCurrBitD    = 0;
G_wRxPrevBitD    = 0;
G_wCrcRegData    = 0;
G_wRxBitCount    = 0;

G_wRxAfAdcData   = 0;
G_wRxNrziCntr    = 0;
G_wRxNrziCurr    = 0;
G_wRxNrziPrev    = 0;
G_wRxNrziTemp    = 0;
G_wRxReferValue  = DC_MID_LEVEL;
G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue  = 0;
G_dRxAdcErrCnt   = 0;

G_wNewBitData    = 0;
G_bRxByteData    = 0;

G_PreStart       = 0;
G_PreOffset      = 120;

G_dSyncDetCnt    = 0;
G_dAdcErrCnt     = 0;
G_dStartErrCnt   = 0;
G_dPloadErrCnt   = 0;
G_dStuffErrCnt   = 0;
G_dCrcErrCnt     = 0;
G_dRcvPktCnt     = 0;

G_dRxAfAdcSumVal = 0;
G_dRxAfAdcCntVal = 0;


%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    G_hDumpFile = fopen('./DumpData/AisDumpData_ch1.bin');
else
    G_hDumpFile = fopen('./DumpData/AisDumpData_ch2.bin');
end
G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');

for nSmpCnt = 1:length(G_pSrcDataCh1)
    if (ENABLE_NOTCH_FLT == 1)
        rX = G_pSrcDataCh1(nSmpCnt);
        rY = G_vNotchDataX(1) + NOTCH_FLT_B(1) * rX;
        G_vNotchDataX(1) = (NOTCH_FLT_B(2) * rX) + (NOTCH_FLT_A(1) * rY) + G_vNotchDataX(2);
        G_vNotchDataX(2) = (NOTCH_FLT_B(3) * rX) + (NOTCH_FLT_A(2) * rY) + G_vNotchDataX(3);
        G_wRxAfAdcData = floor(rY);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nSmpCnt);
    end

    G_vRxRawDataBuff(1:1) = [];
    G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = floor(G_wRxAfAdcData);

    if (ENABLE_GMSK_RX_FLT == 1)
        %{
        nSum = 0;
        for idx = 1:RX_GMSK_BT_0_5_FIR_N
            nSum = nSum + (G_vRxRawDataBuff(idx) * G_vRxGmskCoefficient(idx));
        end
        G_wRxAfAdcData = floor(nSum / RX_GMSK_MAX_DATA_VALUE);
        %}
        %{
        conv_data = conv(G_vRxRawDataBuff, G_vRxGmskCoefficient);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N) / RX_GMSK_MAX_DATA_VALUE);
        %}
        
        conv_data = conv(G_vRxRawDataBuff, impulse_response_of_gmsk);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N));
        

        G_pFilteredData(nSmpCnt) = G_wRxAfAdcData;
    end

    %%%%%if (G_wRxAfAdcData < DC_MIN_LEVEL || G_wRxAfAdcData > DC_MAX_LEVEL)
    if (G_pSrcDataCh1(nSmpCnt) < DC_MIN_LEVEL || G_pSrcDataCh1(nSmpCnt) > DC_MAX_LEVEL)
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = DC_MID_LEVEL * RX_PRE_MAX_BUF_SIZE;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
            G_dRxAdcErrCnt = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > 20)
                G_dRxAdcErrCnt = 0;

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;
            
                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    else
        if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
            G_xPreData.nPntX = (G_xPreData.nPntX + 1);
            index = G_xPreData.nPntX;
            if (G_xPreData.nPntX >= RX_PRE_MAX_BUF_SIZE)
                G_xPreData.nPntX  = 0;
            end

            if(G_xPreData.dCntX >= RX_PRE_MAX_BUF_SIZE)
                G_xPreData.dSumX = G_xPreData.dSumX - G_xPreData.vData(index);
            else
                G_xPreData.dSumX = G_xPreData.dSumX - DC_MID_LEVEL;
                G_xPreData.dCntX = G_xPreData.dCntX + 1;
            end

            G_xPreData.dSumX = G_xPreData.dSumX + G_wRxAfAdcData;
            G_xPreData.wAvrX = G_xPreData.dSumX / RX_PRE_MAX_BUF_SIZE;
            G_xPreData.vData(index) = G_wRxAfAdcData;
    
            G_wRxReferValue = G_xPreData.wAvrX;
        end
    end

    if (G_wRxAfAdcData > G_wRxReferValue)
        G_wRxNrziCurr = 1;
    else
        G_wRxNrziCurr = 0;
    end

    %%%%% ProcessRxDataSwPllRun()
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        %%%%% ProcessRxStatusPreamble();
        G_dRxAfAdcSumVal = G_dRxAfAdcSumVal + G_wRxAfAdcData;
        G_dRxAfAdcCntVal = G_dRxAfAdcCntVal + 1;

        if (G_wRxNrziPrev == G_wRxNrziCurr)
            G_xDotData.wDotCountX = G_xDotData.wDotCountX + 1;
        else
            %%%%%if(G_xDotData.wDotCountX < (OSR * 2 - 3) || G_xDotData.wDotCountX > (OSR * 2 + 3))
            if(G_xDotData.wDotCountX < (OSR * 2 - 3) || G_xDotData.wDotCountX > (OSR * 2 + 3))
                if(G_xDotData.wDotChanged)
                    G_xDotData.wDotPattern = 0;
                    G_xDotData.wDotChanged = 0;
                end

                G_xDotData.wDotCountX = 1;
                G_dRxAfAdcSumVal = G_wRxAfAdcData;
                G_dRxAfAdcCntVal = 1;
            else
                if(G_wRxNrziPrev)
                    G_xDotData.wDotPattern = bitor(G_xDotData.wDotPattern, 1);
                end

                G_xDotData.wDotPattern = bitand(G_xDotData.wDotPattern, uint16(RX_DOT_MAX_CNT_MASK));
                if (G_xDotData.wDotPattern ~= 0x0001 && ...
                    G_xDotData.wDotPattern ~= 0x0002 && ...
                    G_xDotData.wDotPattern ~= 0x0005 && ...
                    G_xDotData.wDotPattern ~= 0x000a && ...
                    G_xDotData.wDotPattern ~= 0x0015 && ...
                    G_xDotData.wDotPattern ~= 0x002a && ...
                    G_xDotData.wDotPattern ~= 0x0055)
                    if (G_xDotData.wDotPattern)
                        G_xDotData.wDotPattern = 0;
                        G_xDotData.wDotChanged = 0;
                    end
                    G_xDotData.wDotCountX = 1;
                    G_dRxAfAdcSumVal = G_wRxAfAdcData;
                    G_dRxAfAdcCntVal = 1;
                else
                    if(G_xDotData.wDotPattern == RX_DOT_START_P_MASK)
                        G_dRxAfAdcSumVal = G_wRxAfAdcData;
                        G_dRxAfAdcCntVal = 1;
                    end

                    G_xDotData.wDotChanged = 1;

                    if(G_xDotData.wDotPattern == RX_DOT_DETCT_P_MASK)
                        if (ENABLE_PLOT1 == 1)
                            figure(1);
                            if (nSmpCnt > G_PreOffset)
                                G_PreStart = nSmpCnt-G_PreOffset;
                            else
                                G_PreStart = 1;
                            end
                            x1 = G_PreStart:nSmpCnt+800;
                            x2 = G_PreStart:nSmpCnt;
                            subplot(2,1,1); plot(x1, G_pSrcDataCh1(G_PreStart:nSmpCnt+800), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('detected\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                            subplot(2,1,2); plot(x2, G_pSrcDataCh1(G_PreStart:nSmpCnt), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('filtered\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                        end

                        G_wRxReferValue = floor(G_dRxAfAdcSumVal / G_dRxAfAdcCntVal);
                        %%%G_wRxReferValue = floor(G_dRxAfAdcSumVal / G_dRxAfAdcCntVal) - 10;

                        G_wRxRunStatus  = RX_MDM_STATUS_START;
                        G_wRxShiftReg   = 0;
                        G_wRxBitCount   = 0;
                        G_wRxPrevBitD   = G_wRxNrziPrev;
                        G_wBitSamplCntr = 1;
                        G_dRxAdcErrCnt  = 0;

                        G_dSwRxPllValue = RX_PLL_HALF;
                        G_dSwRxPllCntrX = 1;
                        G_dSwRxPllSampC = G_wRxNrziPrev;
                        G_dSwRxPllSampP = G_wRxNrziPrev;

                        G_xPreData.nPntX = 0;
                        G_xPreData.dSumX = DC_MID_LEVEL * RX_PRE_MAX_BUF_SIZE;
                        G_xPreData.dCntX = 0;
                        G_xPreData.wAvrX = DC_MID_LEVEL;

                        G_xDotData.wDotPattern = 0;
                        G_xDotData.wDotChanged = 0;
                        G_xDotData.wDotCountX = 0;

                        G_dRxAfAdcSumVal = 0;
                        G_dRxAfAdcCntVal = 0;

                        G_dSyncDetCnt = G_dSyncDetCnt + 1;
                    else
                        G_xDotData.wDotPattern = bitshift(G_xDotData.wDotPattern, 1);
                        G_xDotData.wDotCountX = 1;
                    end
                end
            end
        end

        %if (G_wRxNrziCurr ~= G_wRxNrziPrev)
        %    G_wRxNrziCntr = 1;
        %else
        %    G_wRxNrziCntr = G_wRxNrziCntr + 1;
        %end

        G_wRxNrziPrev = G_wRxNrziCurr;
        G_dSwRxPllSampP = G_wRxNrziCurr;
    else
        G_dSwRxPllSampC = G_wRxNrziCurr;

        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        % 아래 코드 사용시 수신율이 높아짐.
        if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
            if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
            %if(G_wRxNrziCntr <= (OSR - 2))
                G_wRxNrziCntr = G_wRxNrziCntr + 1;
                G_dSwRxPllSampC = G_dSwRxPllSampP;
            else
                G_wRxNrziCntr = 1;
            end
        else
            G_wRxNrziCntr = G_wRxNrziCntr + 1;
        end
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

        if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
            if (G_wRxRunStatus == RX_MDM_STATUS_START)
                if (G_dSwRxPllCntrX >= (OSR * 2 - 2) && G_dSwRxPllCntrX <= (OSR * 2 + 2))
                    G_dSwRxPllValue = RX_PLL_HALF + RX_PLL_STEP;
                end
            end
    
            if (G_dSwRxPllValue < RX_PLL_HALF)
                G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
            else
                G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
            end

            G_dSwRxPllCntrX = 1;
        else
            G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
        end

        G_dSwRxPllSampP = G_dSwRxPllSampC;

        G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
        if(G_dSwRxPllValue >= RX_PLL_FULL)
            G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
        else
            continue;
        end

        G_wRxCurrBitD = G_dSwRxPllSampC;

        %%%%% ProcessRxDataCommonRun()
        G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
        if (G_wRxCurrBitD == G_wRxPrevBitD)
            G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
        else
            G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
        end

        G_wRxPrevBitD = G_wRxCurrBitD;

        switch (G_wRxRunStatus)
            case RX_MDM_STATUS_START
                if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    if (ENABLE_PLOT2 == 1)
                        figure(2);
                        x1 = G_PreStart:nSmpCnt+800;
                        x2 = G_PreStart:nSmpCnt;
                        subplot(2,1,1); plot(x1, G_pSrcDataCh1(G_PreStart:nSmpCnt+800), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('detected\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                        subplot(2,1,2); plot(x2, G_pSrcDataCh1(G_PreStart:nSmpCnt), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('filtered\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                    end

                    %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                    %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();
                    G_wRxBitCount    = 0;
                    G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                else
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if(G_wRxBitCount >= 32)
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dStartErrCnt = G_dStartErrCnt + 1;
                    end
                end

            case RX_MDM_STATUS_PRELOAD
                G_wRxBitCount = G_wRxBitCount + 1;
                if (G_wRxBitCount == 8)
                    G_wRxBitCount = 0;
                    G_wCrcRegData = 0xffff;
                    G_wRxRunStatus= RX_MDM_STATUS_DATA;
                    %ClrRxRawFormTemp();

                    nTemp = bitshift(G_wRxShiftReg, 2);
                    nTemp = bitand(nTemp, 0x00ff);
                    nMsgID = G_vReverDataTableX(nTemp + 1);
                    if (nMsgID < 0)
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dPloadErrCnt = G_dPloadErrCnt + 1;
                    else
                        m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                    end
                end

            case RX_MDM_STATUS_DATA
                if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if(G_wRxBitCount >= (m_wRxMaxBitSize))
                        %%%%%% ResetToRxStatusPreamble()
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_bRxByteData = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dStuffErrCnt = G_dStuffErrCnt + 1;
                        continue;
                    end

                    G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                    G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));

                    %if(bitand(G_wRxBitCount, 0x07) == 0)
                    %    PutDataIntoRxRawBuff(G_bRxByteData);
                    %end

                    if (bitand(bitxor(G_wCrcRegData, G_wNewBitData), 0x0001))           % Pass new bit through CRC calculation
                        G_wCrcRegData = bitxor(bitshift(G_wCrcRegData, -1), 0x8408);   % Xor with the CRC polynomial (X^16 + X^12 + X^5 + 1)
                    else
                        G_wCrcRegData = bitshift(G_wCrcRegData, -1);
                    end
                end

                if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    if (ENABLE_PLOT3 == 1)
                        figure(3);
                        x1 = G_PreStart:nSmpCnt+50;
                        x2 = G_PreStart:nSmpCnt;
                        plot(x1, G_pSrcDataCh1(G_PreStart:nSmpCnt+50), '-x', x2, G_pFilteredData(G_PreStart:nSmpCnt), '-o'); grid; title('filtered\_preamble'); yline(G_wRxReferValue, '-r', 'Refer Value');
                    end

                    if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                        %WritePacketIntoRxRawBuff();

                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_bRxByteData = 0;
                        G_dRcvPktCnt = G_dRcvPktCnt + 1;
                    else
                        %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                        %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                        G_wRxBitCount    = 0;
                        G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                        %G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                        G_bRxByteData    = 0;
                        G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                    end
                end

            otherwise
                warning('Unexpected run status.');
        end
    end
end

figure(9);
%subplot(2,1,1); plot(kkk, detected_data_seq_100(kkk), '-x', kkk, source_data1(kkk), '-o', kkk, 0.5*bit_error_simple_100(kkk), 'k-d', kkk, 0.5*bit_error_100(kkk), 'b-d'); title('estimated sequence (x), source sequence (o), bit error (blue), bit error (direct decision) (black)'); grid;
%subplot(2,1,2); 
bar_x = ["SyncDet" "AdcErr" "StartErr" "PloadErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dPloadErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y);
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')
